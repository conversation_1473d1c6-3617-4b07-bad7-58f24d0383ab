# LettersBot Deployment Guide

This guide covers deploying LettersBot to Cloudflare Pages with D1 database and Browser Rendering.

## Prerequisites

1. **Cloudflare Account**: Sign up at [cloudflare.com](https://cloudflare.com)
2. **Wrangler CLI**: Install globally with `npm install -g wrangler`
3. **Node.js**: Version 18+ required
4. **Git**: For version control and deployment

## Quick Start

```bash
# 1. Clone and setup
git clone <repository-url>
cd letters-bot
npm install

# 2. Login to Cloudflare
wrangler login

# 3. Create production database
wrangler d1 create letters-bot-db-prod

# 4. Update wrangler.toml with the database ID
# 5. Run migrations
wrangler d1 migrations apply DB --env production

# 6. Deploy to Cloudflare Pages
npm run build
wrangler pages deploy build --project-name letters-bot
```

## Detailed Setup

### 1. Database Setup

#### Create Production Database
```bash
# Create the production D1 database
wrangler d1 create letters-bot-db-prod
```

This will output a database ID. Copy it and update `wrangler.toml`:

```toml
[[env.production.d1_databases]]
binding = "DB"
database_name = "letters-bot-db-prod"
database_id = "your-actual-database-id-here"
```

#### Run Migrations
```bash
# Apply migrations to production database
wrangler d1 migrations apply DB --env production

# Verify the schema
wrangler d1 execute DB --env production --command "SELECT name FROM sqlite_master WHERE type='table';"
```

### 2. Environment Variables

#### Required Environment Variables

Set these in the Cloudflare Pages dashboard or via Wrangler:

```bash
# Webhook authentication secret
wrangler pages secret put WEBHOOK_SECRET --project-name letters-bot
# Enter a secure random string when prompted

# Optional: Set environment
wrangler pages secret put NODE_ENV --project-name letters-bot
# Enter "production" when prompted
```

#### Environment Variable Reference

| Variable         | Description                               | Required | Example                     |
| ---------------- | ----------------------------------------- | -------- | --------------------------- |
| `WEBHOOK_SECRET` | Authentication token for /api/run webhook | Yes      | `your-secure-random-string` |
| `NODE_ENV`       | Environment identifier                    | No       | `production`                |

**Note**: Cloudflare Pages only supports "preview" and "production" environments, not "development".

### 3. Browser Rendering Setup

Browser Rendering is automatically available in Cloudflare Workers. No additional setup required.

The binding is configured in `wrangler.toml`:
```toml
[browser]
binding = "BROWSER"
```

### 4. Cloudflare Pages Deployment

#### Option A: Automatic Deployment (Recommended)

1. **Connect Repository**:
   - Go to Cloudflare Pages dashboard
   - Click "Create a project"
   - Connect your Git repository
   - Select the repository

2. **Configure Build Settings**:
   ```
   Build command: npm run build
   Build output directory: build
   Root directory: /
   ```

3. **Environment Variables**:
   - Add `WEBHOOK_SECRET` in the Pages dashboard
   - Add any other required variables

#### Option B: Manual Deployment

```bash
# Build the application
npm run build

# Deploy to Pages
wrangler pages deploy build --project-name letters-bot --env production

# Or deploy with custom domain
wrangler pages deploy build --project-name letters-bot --compatibility-date 2024-12-01
```

### 5. Custom Domain (Optional)

1. **Add Domain in Cloudflare**:
   - Go to Pages dashboard
   - Select your project
   - Go to "Custom domains"
   - Add your domain

2. **DNS Configuration**:
   - Add CNAME record pointing to your Pages URL
   - Or use Cloudflare's nameservers for full management

## Verification

### 1. Test Database Connection
```bash
# Test database access
wrangler d1 execute DB --env production --command "SELECT COUNT(*) FROM best_lines;"
```

### 2. Test API Endpoints

```bash
# Test webhook endpoint (should return 401 without auth)
curl https://your-domain.com/api/run

# Test board endpoint (should return 400 for invalid date)
curl https://your-domain.com/board/invalid-date

# Test service status
curl https://your-domain.com/api/run
```

### 3. Test Frontend

Visit your deployed URL and verify:
- [ ] Home page loads correctly
- [ ] Navigation works
- [ ] Error pages display properly
- [ ] Responsive design works on mobile

## Monitoring and Maintenance

### 1. Logs and Analytics

```bash
# View recent logs
wrangler pages deployment tail --project-name letters-bot

# View analytics in Cloudflare dashboard
# Go to Pages > Your Project > Analytics
```

### 2. Database Maintenance

```bash
# Check database size
wrangler d1 execute DB --env production --command "SELECT COUNT(*) as total_records FROM best_lines;"

# View recent entries
wrangler d1 execute DB --env production --command "SELECT date, created_at FROM best_lines ORDER BY date DESC LIMIT 10;"

# Backup database (export to JSON)
wrangler d1 execute DB --env production --command "SELECT * FROM best_lines;" --json > backup.json
```

### 3. Performance Monitoring

Monitor these metrics in Cloudflare dashboard:
- Request volume and response times
- Error rates (4xx/5xx responses)
- Database query performance
- Browser Rendering usage

## Troubleshooting

### Common Issues

1. **Database Connection Errors**:
   ```
   Error: D1_ERROR: no such table: best_lines
   ```
   **Solution**: Run migrations with `wrangler d1 migrations apply DB --env production`

2. **Authentication Errors**:
   ```
   Error: Unauthorized
   ```
   **Solution**: Set `WEBHOOK_SECRET` environment variable

3. **Build Failures**:
   ```
   Error: Build failed
   ```
   **Solution**: Check Node.js version (18+ required) and run `npm install`

### Debug Commands

```bash
# Check Wrangler configuration
wrangler whoami

# List databases
wrangler d1 list

# Check Pages projects
wrangler pages project list

# View environment variables
wrangler pages secret list --project-name letters-bot
```

## Security Considerations

1. **Webhook Security**:
   - Use a strong, random `WEBHOOK_SECRET`
   - Rotate the secret periodically
   - Monitor webhook usage in logs

2. **Database Security**:
   - D1 databases are automatically secured by Cloudflare
   - Access is restricted to your Workers/Pages
   - Regular backups recommended

3. **Rate Limiting**:
   - Cloudflare provides automatic DDoS protection
   - Consider implementing application-level rate limiting for API endpoints

## Cost Optimization

1. **D1 Database**:
   - Free tier: 5GB storage, 25M reads/month
   - Monitor usage in Cloudflare dashboard

2. **Browser Rendering**:
   - Pay-per-use pricing
   - Optimize solver runtime to minimize costs

3. **Pages**:
   - Free tier: 500 builds/month, unlimited requests
   - Bandwidth included

## Backup and Recovery

### Database Backup
```bash
# Create backup
wrangler d1 execute DB --env production --command "SELECT * FROM best_lines;" --json > backup-$(date +%Y%m%d).json

# Restore from backup (if needed)
# Note: Implement restore script based on your backup format
```

### Code Backup
- Use Git for version control
- Tag releases for easy rollback
- Keep deployment history in Cloudflare Pages

## Support

For issues:
1. Check Cloudflare status page
2. Review Wrangler documentation
3. Check application logs
4. Contact Cloudflare support if needed

---

**Next Steps**: After deployment, set up monitoring and consider implementing automated daily solver runs via cron triggers.
