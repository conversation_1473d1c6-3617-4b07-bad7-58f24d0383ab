# LettersBot – Full-Flow Implementation Guide

*Version 0.2 – living document (2025-06-16)*

---
## 0. Project Goals

| #   | Goal                                                                                                | Success Metric                                                                                          |
| --- | --------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------- |
| 1   | **Find the globally-optimal 5-word line every day**                                                 | Solver returns identical totals to brute-force ground-truth on test boards.                             |
| 2   | **Run autonomously once per day** via an authenticated webhook; keep solver opaque to public users. | Webhook returns HTTP 202 and starts a Cloudflare Browser Rendering (CBR) job; no public trigger exists. |
| 3   | **Expose read-only results** (five words, per-round best, total) at `/board/YYYY-MM-DD`.            | Endpoint reachable on the Pages `.pages.dev` domain; returns JSON + minimal UI card.                    |
| 4   | **Store persistent history** in Cloudflare **D1** (SQLite).                                         | Table `best_lines` has ≥90 days of rows; inserts idempotent per day.                                    |
| 5   | **Stay within free-tier CBR & Functions limits**                                                    | ≤ 200 CBR minutes / day, Functions CPU ≤ 10 ms / request (except solver job).                           |

---
## 1. Problem Recap

* **Game**: “Letters” – 5×5 grid, 5 turns, letter + word multipliers.  
* **Input**: today’s board scraped from the Letters website.  
* **Output**: best overall 5-word sequence, per-round greedy bests.  
* **Hard part**: future tiles are unknown until a move is actually played → search must interactively branch.

---
## 2. System Architecture (condensed)

                      ┌──────────────┐
    webhook (POST) ─▶ │  /api/run    │ Pages Function
                      └──────┬───────┘  (auth checks)
                             │
             triggers CBR session via `WITH_BROWSERS`
                             │
  ┌──────── solver worker (Playwright) ─────┐
  │ 1. fetch board                          │
  │ 2. beam search w/ contexts              │
  │ 3. compute incumbent best               │
  └─────────────────┬───────────────────────┘
                    │ writes row
              D1 ───┘  best_lines (date,k,v)
                    │
           public GET /board/2025-06-16
                    │ reads row
                    ▼
             SvelteKit page-load → display

---
## 3. Dictionary Compilation Pipeline

We ship a one-time script `scripts/compileDictionary.ts` that converts a plain text list (`en.txt`, one word per line) into a compact binary (`dict.bin`).

```bash
# run during CI → artifact committed to repo
npx ts-node scripts/compileDictionary.ts ./data/en.txt ./src/lib/dict.bin
```

### Record layout
| Bytes | Field            | Notes                              |
| ----- | ---------------- | ---------------------------------- |
| 2 LE  | `wordByteLen`    | UTF-8 length                       |
| 2 LE  | `letterScoreSum` | Σ Scrabble scores (no multipliers) |
| 1     | `wordLen`        | 2…25                               |
| 26    | `hist`           | Uint8Array\[26] letter histogram   |
| N     | `utf8`           | word bytes                         |

Header (`uint32 nEntries`) precedes the records.

Below is the complete, ready-to-run `scripts/compileDictionary.ts` referenced above.

```ts
// scripts/compileDictionary.ts
//------------------------------------------------------------
//  Compile a plain-text word list (one word per line) into a
//  compact binary file used by LettersBot at runtime.
//
//  Usage:
//      npx ts-node scripts/compileDictionary.ts data/en.txt dict.bin
//
//  Record layout (little-endian):
//      [header]
//          uint32  nEntries
//      nEntries × {
//          uint16  wordByteLen      // UTF-8 length
//          uint16  letterScoreSum   // Σ Scrabble scores (A=1, B=3…)
//          uint8   wordLen          // 2 – 25 inclusive
//          uint8×26 letterFreq      // histogram for A…Z
//          bytes   utf8Word         // actual bytes
//      }
//------------------------------------------------------------
import { readFileSync, writeFileSync } from "node:fs";
import { basename } from "node:path";

//------------------------------------------------------------
// 1.  Parse CLI args
//------------------------------------------------------------
const [, , inPath = "data/en.txt", outPath = "data/dict.bin"] = process.argv;
console.log(`[compileDictionary] input → ${basename(inPath)}, output → ${basename(outPath)}`);

//------------------------------------------------------------
// 2.  Letter score table (Scrabble-style)
//------------------------------------------------------------
const LETTER_SCORES: Record<string, number> = {
  A: 1, B: 3, C: 3, D: 2, E: 1, F: 4, G: 2, H: 4, I: 1,
  J: 8, K: 5, L: 1, M: 3, N: 1, O: 1, P: 3, Q: 10, R: 1,
  S: 1, T: 1, U: 1, V: 4, W: 4, X: 8, Y: 4, Z: 10,
};

//------------------------------------------------------------
// 3.  Read word list & build binary array
//------------------------------------------------------------
const lines = readFileSync(inPath, "utf8").split(/\\r?\\n/);
const chunks: number[] = [];
let entryCount = 0;

for (const raw of lines) {
  const word = raw.trim().toUpperCase();
  if (word.length < 2 || word.length > 25 || /[^A-Z]/.test(word)) continue;

  // 3a. Histogram & base score
  const hist = new Uint8Array(26);
  let score = 0;
  for (const ch of word) {
    const idx = ch.charCodeAt(0) - 65;
    hist[idx]++;
    score += LETTER_SCORES[ch];
  }

  // 3b. Serialise record header
  const utf8 = new TextEncoder().encode(word);
  const len = utf8.length;

  chunks.push(len & 0xff, len >>> 8);           // uint16 wordByteLen
  chunks.push(score & 0xff, score >>> 8);       // uint16 letterScoreSum
  chunks.push(word.length);                     // uint8 wordLen
  for (let i = 0; i < 26; i++) chunks.push(hist[i]); // 26-byte histogram
  chunks.push(...utf8);                         // word bytes

  entryCount++;
}

//------------------------------------------------------------
// 4.  Prepend header & flush to disk
//------------------------------------------------------------
const header = new Uint8Array(4);
new DataView(header.buffer).setUint32(0, entryCount, true);
writeFileSync(outPath, Buffer.concat([header, Buffer.from(chunks)]));

console.log(`[compileDictionary] wrote ${entryCount} entries → ${(chunks.length + 4).toLocaleString()} bytes`);
```

## 4. Core Daily Solver Loop – `solveDailyBoard`

```ts
// src/lib/solver/solveDailyBoard.ts
//------------------------------------------------------------
import TinyQueue from "tinyqueue";
import { BROWSER } from "@cloudflare/browser-rendering"; // Cloudflare binding
import { Hungarian } from "./hungarian";
import type { Board, Word, GameNode } from "./types";
import { bestWords } from "./bestWords";
import { insertBestLine } from "../db"; // D1 helper

export async function solveDailyBoard(dateISO: string): Promise<void> {
  //----------------------------------------------------------
  // 0. open a cloudflare browser session & grab initial grid
  //----------------------------------------------------------
  const session = await BROWSER.launch();
  const page    = await session.newPage();
  await page.goto("https://play.thelettersgame.com/");
  const initialBoard: Board = await scrapeBoard(page);

  //----------------------------------------------------------
  // 1. priority-queue search (beam/B&B)
  //----------------------------------------------------------
  const pq = new TinyQueue<GameNode>([], (a,b)=>b.upperBound-a.upperBound);
  const incumbent: { node?: GameNode } = {};
  pq.push({ board: initialBoard, turn: 0, total: 0, moves: [],
            upperBound: upperBoundForBoard(initialBoard) });

  const seen = new Map<string, number>();

  while (pq.length) {
    const node = pq.pop()!;
    if (incumbent.node && node.upperBound <= incumbent.node.total) continue;
    if (node.turn === 5) {
      if (!incumbent.node || node.total > incumbent.node.total) incumbent.node = node;
      continue;
    }

    const key = hashBoard(node.board)+"@"+node.turn;
    if (seen.has(key) && seen.get(key)! >= node.total) continue;
    seen.set(key, node.total);

    const K = Math.max(60/(node.turn+1), 20)|0;
    const cand = bestWords(node.board, K);

    // run candidates sequentially inside this same browser session
    for (const w of cand) {
      await playWord(page, w.positions); // clicks & submit
      const nextBoard: Board = await scrapeBoard(page);
      const nextTotal = node.total + w.score;
      const nextUpper = nextTotal + (4-node.turn) * upperBoundForBoard(nextBoard);
      pq.push({ board: nextBoard,
                turn: (node.turn+1) as 0|1|2|3|4|5,
                total: nextTotal,
                moves: [...node.moves, w],
                upperBound: nextUpper });
      await undoLastMove(page); // reloads state so we can try next candidate
    }
  }

  if (!incumbent.node) throw new Error("No valid path");

  //----------------------------------------------------------
  // 2. compute greedy per-round maxima on a fresh board
  //----------------------------------------------------------
  await page.reload();
  let greedyBoard: Board = await scrapeBoard(page);
  const perRound: Word[] = [];
  for (let r=0; r<5; r++) {
    const w = bestWords(greedyBoard, 1)[0];
    perRound.push(w);
    await playWord(page, w.positions);
    greedyBoard = await scrapeBoard(page);
  }

  //----------------------------------------------------------
  // 3. persist & teardown
  //----------------------------------------------------------
  const bestLine = incumbent.node.moves.map(m=>m.value).join("-");
  await insertBestLine(dateISO, {
    total : incumbent.node.total,
    words : incumbent.node.moves.map(m=>m.value),
    perRound: perRound.map(m=>({ word:m.value, score:m.score }))
  });

  await session.close();
}
```

#### Implementation notes

`scrapeBoard`, `playWord`, `undoLastMove`, `hashBoard`, and `upperBoundForBoard` live in adjacent util files.

The loop re-uses one CBR session to stay within limits; it reloads/undos to branch.

`insertBestLine` is a thin wrapper around a D1 prepared statement.

