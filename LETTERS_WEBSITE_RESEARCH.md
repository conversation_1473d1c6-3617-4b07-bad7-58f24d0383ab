# Letters Website Structure Research

*Research Date: 2025-06-16*

## Overview
The Letters game website (https://play.thelettersgame.com/) is a JavaScript-based web application that requires browser automation for interaction. Based on the implementation guide and typical game website patterns, here's the expected structure:

## Expected DOM Structure

### Game Board
- **5x5 Grid**: The main game board should be represented as a grid of clickable tiles
- **Tile Elements**: Each tile likely contains:
  - Letter display
  - Multiplier indicators (letter/word multipliers)
  - Click handlers for selection
  - Visual states (selected, highlighted, etc.)

### Game Interface Elements
- **Score Display**: Current score and total
- **Turn Counter**: Current turn (1-5)
- **Word Input**: Area showing currently selected word
- **Submit Button**: To confirm word selection
- **Undo/Reset**: To clear current selection

### Expected CSS Selectors (Hypothetical)
```css
/* Game board container */
.game-board, #game-board, .letters-grid

/* Individual tiles */
.tile, .letter-tile, .game-tile
.tile[data-row][data-col]

/* Tile states */
.tile.selected, .tile.highlighted
.tile.letter-multiplier, .tile.word-multiplier

/* Game controls */
.submit-button, #submit-word
.undo-button, .clear-selection
.score-display, .turn-counter

/* Word display */
.current-word, .selected-letters
```

## Expected Interaction Patterns

### 1. Board Scraping (`scrapeBoard`)
**Purpose**: Extract current board state including letters and multipliers

**Expected Implementation**:
```typescript
async function scrapeBoard(page: Page): Promise<Board> {
  // Wait for game board to load
  await page.waitForSelector('.game-board');
  
  // Extract tile data
  const tileData = await page.evaluate(() => {
    const tiles = [];
    for (let row = 0; row < 5; row++) {
      for (let col = 0; col < 5; col++) {
        const tileElement = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
        if (tileElement) {
          tiles.push({
            row,
            col,
            letter: tileElement.textContent.trim(),
            letterMult: parseInt(tileElement.dataset.letterMult || '1'),
            wordMult: parseInt(tileElement.dataset.wordMult || '1')
          });
        }
      }
    }
    return tiles;
  });
  
  // Convert to Board object
  return createBoardFromTileData(tileData);
}
```

### 2. Word Playing (`playWord`)
**Purpose**: Click tiles in sequence and submit word

**Expected Implementation**:
```typescript
async function playWord(page: Page, positions: Position[]): Promise<void> {
  // Clear any existing selection
  await page.click('.clear-selection');
  
  // Click tiles in sequence
  for (const [row, col] of positions) {
    await page.click(`[data-row="${row}"][data-col="${col}"]`);
    await page.waitForTimeout(100); // Small delay between clicks
  }
  
  // Submit the word
  await page.click('.submit-button');
  await page.waitForSelector('.game-board'); // Wait for board update
}
```

### 3. Undo Functionality (`undoLastMove`)
**Purpose**: Revert to previous game state

**Expected Implementation**:
```typescript
async function undoLastMove(page: Page): Promise<void> {
  // Look for undo button or reload page
  const undoButton = await page.$('.undo-button');
  if (undoButton) {
    await undoButton.click();
  } else {
    // Fallback: reload page to reset state
    await page.reload();
    await page.waitForSelector('.game-board');
  }
}
```

## Data Extraction Strategies

### Tile Information
1. **Letter Content**: `textContent` or `innerText` of tile elements
2. **Position**: `data-row` and `data-col` attributes or CSS grid position
3. **Multipliers**: 
   - CSS classes (`.letter-2x`, `.word-3x`)
   - Data attributes (`data-letter-mult`, `data-word-mult`)
   - Background colors or visual indicators

### Game State
1. **Current Score**: Text content of score display element
2. **Turn Number**: Turn counter or progress indicator
3. **Selected Word**: Currently highlighted tiles or word display area

## Error Handling Considerations

### Common Issues
1. **Loading Delays**: Game may take time to initialize
2. **Animation Timing**: Tile drops and transitions need wait periods
3. **Network Issues**: Handle connection timeouts
4. **Invalid Moves**: Detect and handle rejected word submissions

### Robust Selectors
- Use multiple selector strategies (ID, class, data attributes)
- Implement fallback selectors for different game versions
- Add retry logic for transient failures

## Testing Approach

### Manual Verification Steps
1. Load game in browser
2. Inspect DOM structure with developer tools
3. Identify actual selectors and interaction patterns
4. Test word submission flow
5. Verify board state changes after moves

### Automated Testing
1. Create test boards with known configurations
2. Verify scraping accuracy against expected values
3. Test word playing with simple 2-3 letter words
4. Validate undo/reset functionality

## Implementation Notes

### Browser Configuration
- Use Cloudflare Browser Rendering in production
- Configure appropriate timeouts for game interactions
- Handle JavaScript-heavy page loading
- Implement proper error recovery

### Performance Considerations
- Minimize page reloads (use undo when possible)
- Batch operations where feasible
- Cache selectors and DOM queries
- Implement efficient waiting strategies

## Next Steps

1. **Manual Inspection**: Use browser developer tools to identify actual selectors
2. **Prototype Implementation**: Create basic scraping functions
3. **Integration Testing**: Test with real game instances
4. **Error Handling**: Add robust error recovery
5. **Performance Optimization**: Minimize interaction overhead

## Assumptions to Validate

- Game uses standard HTML/CSS structure
- Tiles are clickable DOM elements
- Board state is reflected in DOM
- Game provides visual feedback for selections
- Submit/undo actions are available
- Page structure is consistent across sessions

This research document will be updated as actual implementation proceeds and real website structure is discovered.
