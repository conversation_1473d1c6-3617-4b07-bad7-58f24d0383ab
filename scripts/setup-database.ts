#!/usr/bin/env tsx
/**
 * Database setup script for LettersBot
 * 
 * This script helps initialize the D1 database for both local development
 * and production environments.
 */

import { readFileSync } from 'fs';
import { join } from 'path';

/**
 * Database setup commands for different environments
 */
const COMMANDS = {
	// Create local D1 database
	createLocal: 'npx wrangler d1 create letters-bot-db-local',
	
	// Create production D1 database
	createProd: 'npx wrangler d1 create letters-bot-db-prod',
	
	// Execute migrations on local database
	migrateLocal: (migrationFile: string) => 
		`npx wrangler d1 execute letters-bot-db-local --local --file=${migrationFile}`,
	
	// Execute migrations on production database
	migrateProd: (migrationFile: string) => 
		`npx wrangler d1 execute letters-bot-db-prod --file=${migrationFile}`,
	
	// List local databases
	listLocal: 'npx wrangler d1 list',
	
	// Query local database
	queryLocal: (query: string) => 
		`npx wrangler d1 execute letters-bot-db-local --local --command="${query}"`,
	
	// Query production database
	queryProd: (query: string) => 
		`npx wrangler d1 execute letters-bot-db-prod --command="${query}"`
};

/**
 * Migration files in order
 */
const MIGRATION_FILES = [
	'migrations/0001_initial_schema.sql',
	'migrations/0002_best_lines_table.sql'
];

/**
 * Print usage information
 */
function printUsage() {
	console.log(`
LettersBot Database Setup Script

Usage: tsx scripts/setup-database.ts <command> [options]

Commands:
  create-local     Create local D1 database for development
  create-prod      Create production D1 database
  migrate-local    Run migrations on local database
  migrate-prod     Run migrations on production database
  verify-local     Verify local database schema
  verify-prod      Verify production database schema
  status-local     Show local database status
  status-prod      Show production database status
  help             Show this help message

Examples:
  tsx scripts/setup-database.ts create-local
  tsx scripts/setup-database.ts migrate-local
  tsx scripts/setup-database.ts verify-local

Note: Make sure you have wrangler CLI installed and authenticated.
`);
}

/**
 * Execute a shell command and return the result
 */
async function executeCommand(command: string): Promise<{ success: boolean; output: string; error?: string }> {
	const { exec } = await import('child_process');
	const { promisify } = await import('util');
	const execAsync = promisify(exec);

	try {
		console.log(`Executing: ${command}`);
		const { stdout, stderr } = await execAsync(command);
		
		if (stderr && !stderr.includes('Warning')) {
			console.warn('Command stderr:', stderr);
		}
		
		console.log('Command output:', stdout);
		return { success: true, output: stdout };
	} catch (error: any) {
		console.error('Command failed:', error.message);
		return { success: false, output: '', error: error.message };
	}
}

/**
 * Create a new D1 database
 */
async function createDatabase(environment: 'local' | 'prod') {
	console.log(`Creating ${environment} D1 database...`);
	
	const command = environment === 'local' ? COMMANDS.createLocal : COMMANDS.createProd;
	const result = await executeCommand(command);
	
	if (result.success) {
		console.log(`✅ ${environment} database created successfully`);
		console.log('📝 Remember to update wrangler.toml with the database ID from the output above');
	} else {
		console.error(`❌ Failed to create ${environment} database`);
	}
	
	return result.success;
}

/**
 * Run migrations on a database
 */
async function runMigrations(environment: 'local' | 'prod') {
	console.log(`Running migrations on ${environment} database...`);
	
	for (const migrationFile of MIGRATION_FILES) {
		console.log(`Applying migration: ${migrationFile}`);
		
		// Check if migration file exists
		try {
			readFileSync(migrationFile, 'utf8');
		} catch (error) {
			console.error(`❌ Migration file not found: ${migrationFile}`);
			return false;
		}
		
		const command = environment === 'local' 
			? COMMANDS.migrateLocal(migrationFile)
			: COMMANDS.migrateProd(migrationFile);
		
		const result = await executeCommand(command);
		
		if (!result.success) {
			console.error(`❌ Failed to apply migration: ${migrationFile}`);
			return false;
		}
	}
	
	console.log(`✅ All migrations applied successfully to ${environment} database`);
	return true;
}

/**
 * Verify database schema
 */
async function verifySchema(environment: 'local' | 'prod') {
	console.log(`Verifying ${environment} database schema...`);
	
	const queries = [
		"SELECT name FROM sqlite_master WHERE type='table' AND name='best_lines'",
		"PRAGMA table_info(best_lines)",
		"SELECT name FROM sqlite_master WHERE type='index' AND tbl_name='best_lines'"
	];
	
	for (const query of queries) {
		const command = environment === 'local' 
			? COMMANDS.queryLocal(query)
			: COMMANDS.queryProd(query);
		
		const result = await executeCommand(command);
		
		if (!result.success) {
			console.error(`❌ Schema verification failed for ${environment} database`);
			return false;
		}
	}
	
	console.log(`✅ ${environment} database schema verified successfully`);
	return true;
}

/**
 * Show database status
 */
async function showStatus(environment: 'local' | 'prod') {
	console.log(`Checking ${environment} database status...`);
	
	const queries = [
		"SELECT COUNT(*) as record_count FROM best_lines",
		"SELECT date FROM best_lines ORDER BY date DESC LIMIT 5",
		"SELECT COUNT(*) as total_tables FROM sqlite_master WHERE type='table'"
	];
	
	for (const query of queries) {
		const command = environment === 'local' 
			? COMMANDS.queryLocal(query)
			: COMMANDS.queryProd(query);
		
		await executeCommand(command);
	}
}

/**
 * Main function
 */
async function main() {
	const args = process.argv.slice(2);
	const command = args[0];
	
	if (!command || command === 'help') {
		printUsage();
		return;
	}
	
	switch (command) {
		case 'create-local':
			await createDatabase('local');
			break;
		
		case 'create-prod':
			await createDatabase('prod');
			break;
		
		case 'migrate-local':
			await runMigrations('local');
			break;
		
		case 'migrate-prod':
			await runMigrations('prod');
			break;
		
		case 'verify-local':
			await verifySchema('local');
			break;
		
		case 'verify-prod':
			await verifySchema('prod');
			break;
		
		case 'status-local':
			await showStatus('local');
			break;
		
		case 'status-prod':
			await showStatus('prod');
			break;
		
		default:
			console.error(`Unknown command: ${command}`);
			printUsage();
			process.exit(1);
	}
}

// Run the script
if (require.main === module) {
	main().catch(error => {
		console.error('Script failed:', error);
		process.exit(1);
	});
}
