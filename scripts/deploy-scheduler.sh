#!/bin/bash

# LettersBot Scheduler Deployment Script
# This script deploys the separate Cloudflare Worker that handles cron triggers

set -e # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
WORKER_NAME="letters-bot-scheduler"
ENVIRONMENT="production"

# Functions
log_info() {
	echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
	echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
	echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
	echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
	log_info "Checking prerequisites..."

	# Check if wrangler is installed
	if ! command -v wrangler &>/dev/null; then
		log_error "Wrangler CLI is not installed. Please install it with: npm install -g wrangler"
		exit 1
	fi

	# Check if logged in to Cloudflare
	if ! wrangler whoami &>/dev/null; then
		log_error "Not logged in to Cloudflare. Please run: wrangler login"
		exit 1
	fi

	# Check if worker.ts exists
	if [ ! -f "worker.ts" ]; then
		log_error "worker.ts file not found. Please ensure you're in the project root directory."
		exit 1
	fi

	# Check if wrangler-scheduler.toml exists
	if [ ! -f "wrangler-scheduler.toml" ]; then
		log_error "wrangler-scheduler.toml file not found. Please ensure you're in the project root directory."
		exit 1
	fi

	log_success "Prerequisites check completed"
}

check_secrets() {
	log_info "Checking required secrets..."

	# Check if WEBHOOK_SECRET is set
	if ! wrangler secret list --config wrangler-scheduler.toml --env "$ENVIRONMENT" 2>/dev/null | grep -q "WEBHOOK_SECRET"; then
		log_warning "WEBHOOK_SECRET not found for $ENVIRONMENT environment"
		log_info "You'll need to set it after deployment with:"
		log_info "wrangler secret put WEBHOOK_SECRET --config wrangler-scheduler.toml --env $ENVIRONMENT"
	else
		log_success "WEBHOOK_SECRET is configured"
	fi
}

deploy_worker() {
	log_info "Deploying scheduler worker to $ENVIRONMENT..."

	# Deploy the worker
	wrangler deploy --config wrangler-scheduler.toml --env "$ENVIRONMENT"

	log_success "Worker deployed successfully"
}

verify_deployment() {
	log_info "Verifying deployment..."

	# Get the worker URL
	WORKER_URL="https://$WORKER_NAME.$USER.workers.dev"

	# Test health endpoint
	log_info "Testing health endpoint..."
	if curl -s "$WORKER_URL/health" | grep -q "healthy"; then
		log_success "Health check passed"
	else
		log_warning "Health check failed - worker may still be starting up"
	fi

	# Show worker information
	log_info "Worker information:"
	echo "  Name: $WORKER_NAME"
	echo "  URL: $WORKER_URL"
	echo "  Environment: $ENVIRONMENT"
	echo "  Cron Schedule: 0 6 * * * (daily at 6 AM UTC)"
}

show_next_steps() {
	log_info "Next steps:"
	echo
	echo "1. Set the WEBHOOK_SECRET if not already done:"
	echo "   wrangler secret put WEBHOOK_SECRET --config wrangler-scheduler.toml --env $ENVIRONMENT"
	echo
	echo "2. Update LETTERS_BOT_DOMAIN in wrangler-scheduler.toml with your actual domain"
	echo
	echo "3. Test the manual trigger:"
	echo "   curl -X POST https://$WORKER_NAME.$USER.workers.dev/trigger"
	echo
	echo "4. Monitor the worker logs:"
	echo "   wrangler tail --config wrangler-scheduler.toml --env $ENVIRONMENT"
	echo
	echo "5. The worker will automatically run daily at 6 AM UTC"
}

main() {
	echo "🚀 LettersBot Scheduler Deployment"
	echo "=================================="
	echo

	check_prerequisites
	echo

	check_secrets
	echo

	deploy_worker
	echo

	verify_deployment
	echo

	show_next_steps
	echo

	log_success "🎉 Scheduler deployment completed!"
}

# Handle script interruption
trap 'log_error "Deployment interrupted"; exit 1' INT TERM

# Run main function
main "$@"
