-- Migration: Create best_lines table for LettersBot
-- Date: 2025-06-17
-- Description: Creates the primary table for storing daily solver results

-- Best lines table to store optimal solutions for each day
-- This table stores the results from the daily Letters game solver
CREATE TABLE IF NOT EXISTS best_lines (
    date TEXT PRIMARY KEY,           -- Date in YYYY-MM-DD format (e.g., "2025-06-17")
    data TEXT NOT NULL,              -- JSON-serialized BestLineResult containing:
                                     -- {
                                     --   "total": number,
                                     --   "words": string[],
                                     --   "perRound": Array<{word: string, score: number}>
                                     -- }
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for efficient queries
CREATE INDEX IF NOT EXISTS idx_best_lines_date ON best_lines(date);
CREATE INDEX IF NOT EXISTS idx_best_lines_created_at ON best_lines(created_at);

-- Ensure the table structure matches our TypeScript interfaces
-- This table supports:
-- 1. Idempotent inserts (INSERT OR REPLACE based on date)
-- 2. Fast lookups by date for the /board/[date] endpoint
-- 3. Chronological ordering for historical data
-- 4. JSON storage for flexible result data structure
