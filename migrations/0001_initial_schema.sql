-- Initial schema for LettersBot database
-- This creates the best_lines table for storing daily solver results

-- Best lines table to store optimal solutions for each day
-- This is the primary table for the LettersBot system
CREATE TABLE IF NOT EXISTS best_lines (
    date TEXT PRIMARY KEY,           -- Date in YYYY-MM-DD format
    data TEXT NOT NULL,              -- JSON-serialized BestLineResult
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Index for efficient date-based queries
CREATE INDEX IF NOT EXISTS idx_best_lines_date ON best_lines(date);
CREATE INDEX IF NOT EXISTS idx_best_lines_created_at ON best_lines(created_at);
