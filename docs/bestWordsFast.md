# bestWordsFast Function Documentation

## Overview

The `bestWordsFast` function is an optimized algorithm for finding the highest-scoring words in the Letters game. It efficiently searches through a dictionary of valid words and finds the best possible placements on a 5x5 game board, taking into account letter and word multipliers.

## Key Features

- **Ultra-fast performance**: Typically completes in 1-8ms for most boards
- **Multiplier optimization**: Prioritizes tiles with higher multipliers for better scores
- **Hybrid algorithm**: Combines fast greedy approximation with precise Hungarian algorithm
- **Memory efficient**: Reasonable memory usage even for large word counts
- **Comprehensive**: Searches through 50,000+ dictionary entries

## Algorithm Overview

The function uses a three-phase approach:

### Phase 1: Fast Filtering
- Processes dictionary entries in a hybrid order (simple words first, then high-scoring words)
- Uses histogram checking to quickly eliminate impossible words
- Applies greedy scoring for fast approximation
- Maintains a running list of top candidates

### Phase 2: Precise Scoring
- Uses the Hungarian algorithm for optimal tile assignment
- Only applied to the top candidates from Phase 1
- Provides exact optimal scores for final ranking

### Phase 3: Position Finding
- Finds actual board positions for each word
- Prioritizes tiles with higher multipliers
- Returns complete Word objects with positions and scores

## Performance Characteristics

| K Value | Typical Time | Memory Usage | Use Case |
|---------|-------------|--------------|----------|
| 5-10    | 1-3ms       | 3-5MB       | Real-time UI |
| 25-50   | 2-5ms       | 5-15MB      | Interactive analysis |
| 100-200 | 3-8ms       | 15-47MB     | Comprehensive search |

## Usage Examples

### Basic Usage
```typescript
import { Board } from './models/Board';
import { bestWordsFast } from './bestWordsFast';

const board = Board.createRandom();
const topWords = bestWordsFast(board, 10);

console.log(`Best word: ${topWords[0].letters} (${topWords[0].score} points)`);
```

### Performance Testing
```typescript
const startTime = performance.now();
const words = bestWordsFast(board, 50);
const endTime = performance.now();

console.log(`Found ${words.length} words in ${endTime - startTime}ms`);
```

### Analyzing Results
```typescript
const words = bestWordsFast(board, 100);

// Top scoring words
const topScores = words.slice(0, 5).map(w => `${w.letters}(${w.score})`);
console.log('Top words:', topScores.join(', '));

// Average score
const avgScore = words.reduce((sum, w) => sum + w.score, 0) / words.length;
console.log(`Average score: ${avgScore.toFixed(1)}`);

// Score distribution
const scoreRanges = {
  high: words.filter(w => w.score >= 50).length,
  medium: words.filter(w => w.score >= 20 && w.score < 50).length,
  low: words.filter(w => w.score < 20).length
};
console.log('Score distribution:', scoreRanges);
```

## Technical Details

### Dictionary Format
The function uses a binary dictionary format (`dict.bin`) for fast loading:
- Pre-computed letter histograms for quick filtering
- Sorted entries for efficient processing
- Compressed format for memory efficiency

### Multiplier Prioritization
The algorithm ensures that when multiple tiles with the same letter are available, it prioritizes those with higher multiplier values:
- Letter multipliers (2x, 3x letter score)
- Word multipliers (2x, 3x total word score)
- Combined multiplier value (letterMult × wordMult)

### Game Rules
- Tiles don't need to be adjacent (any tiles on the board can be used)
- Each tile can only be used once per word
- Standard Scrabble letter scoring applies
- Multipliers are applied according to Letters game rules

## Optimization Notes

### Recent Improvements
1. **Fixed multiplier prioritization bug**: Now correctly selects tiles with best multipliers
2. **Hybrid dictionary processing**: Balances simple word discovery with high-scoring words
3. **Early termination**: Stops processing when score bounds are exceeded
4. **Memory optimization**: Efficient data structures and garbage collection

### Performance Tips
1. **Cache dictionary loading**: The first call takes longer due to dictionary loading
2. **Reasonable K values**: K=25-50 provides good balance of speed and completeness
3. **Batch processing**: Process multiple boards in sequence for better performance
4. **Memory monitoring**: Monitor memory usage for very large K values

## Testing

The function includes comprehensive tests covering:
- **Multiplier prioritization**: Ensures best tiles are selected
- **Performance benchmarks**: Validates speed and memory usage
- **Edge cases**: Handles K=0, K=1, and large K values
- **Word validity**: Verifies correct word formation and scoring

Run tests with:
```bash
npx tsx scripts/test-bestwords-fast.ts
```

## Troubleshooting

### Common Issues

1. **No words found**: Check if dictionary file exists at `src/lib/dict.bin`
2. **Slow performance**: Reduce K value or check for memory pressure
3. **Incorrect scores**: Verify board multipliers are set correctly
4. **Memory issues**: Use smaller K values or implement result streaming

### Debug Tools

Use the debug scripts for troubleshooting:
```bash
npx tsx debug-bestwords.ts           # Basic functionality test
npx tsx debug-bestwords-detailed.ts  # Dictionary and board analysis
npx tsx debug-word-objects.ts        # Word object validation
```

## Future Improvements

Potential areas for further optimization:
1. **Parallel processing**: Multi-threaded dictionary processing
2. **Incremental updates**: Cache results for similar boards
3. **Adaptive algorithms**: Adjust strategy based on board characteristics
4. **Memory streaming**: Process results in chunks for very large K values
