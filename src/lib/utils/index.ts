/**
 * Utility module exports
 *
 * This module contains shared utility functions used across the application.
 */

// Dictionary utilities
export {
	loadDictionary,
	getDictionary,
	validateWord,
	calculateBaseScore,
	Dictionary,
	type DictionaryEntry
} from './dictionary';

// Word validation utilities
export {
	isValidWord,
	getWordBaseScore,
	calculateWordScore,
	findValidWords,
	isValidWordPath,
	createWordFromPositions,
	getWordStats
} from './wordValidation';

// Scoring utilities
export {
	calculateScoringBreakdown,
	calculateMaxPossibleScore,
	calculateScoringEfficiency,
	findBestScoringPositions,
	calculateHighValueBonus,
	calculateLengthBonus,
	calculateTotalScore,
	analyzePositionValue,
	getWordSetStats,
	compareWords,
	type ScoringBreakdown
} from './scoring';

// Board transition utilities
export {
	applyWordWithTracking,
	generateNewTile,
	simulateTransitions,
	analyzeBoardStability,
	findVolatilePositions,
	calculateTransitionEntropy,
	DEFAULT_TILE_CONFIG,
	type TileGenerationConfig,
	type TransitionResult
} from './boardTransitions';
