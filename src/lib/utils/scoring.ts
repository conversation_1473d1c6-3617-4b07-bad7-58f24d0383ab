/**
 * Advanced scoring utilities for the Letters game
 *
 * This module provides comprehensive scoring logic including multipliers,
 * bonuses, and scoring analysis functions.
 */

import { LETTER_SCORES, type Tile } from '../types';
import type { Board } from '../models/Board';
import { Word } from '../models/Word';

/**
 * Detailed scoring breakdown for analysis
 */
export interface ScoringBreakdown {
	baseScore: number;
	letterMultiplierBonus: number;
	wordMultiplierBonus: number;
	totalScore: number;
	letterDetails: Array<{
		letter: string;
		position: [number, number];
		baseValue: number;
		letterMult: number;
		wordMult: number;
		effectiveValue: number;
	}>;
}

/**
 * Calculate detailed scoring breakdown for a word
 */
export function calculateScoringBreakdown(word: Word, board: Board): ScoringBreakdown {
	let baseScore = 0;
	let letterMultiplierBonus = 0;
	let totalWordMultiplier = 1;
	const letterDetails: ScoringBreakdown['letterDetails'] = [];

	// Process each letter
	for (let i = 0; i < word.positions.length; i++) {
		const [row, col] = word.positions[i];
		const tile = board.getTile(row, col);

		if (!tile) {
			throw new Error(`Invalid position [${row}, ${col}] in word`);
		}

		const letter = word.letters[i].toUpperCase();
		const baseValue = LETTER_SCORES[letter] || 0;
		const letterMult = tile.letterMult;
		const wordMult = tile.wordMult;
		const effectiveValue = baseValue * letterMult;

		baseScore += baseValue;
		letterMultiplierBonus += effectiveValue - baseValue;
		totalWordMultiplier *= wordMult;

		letterDetails.push({
			letter,
			position: [row, col],
			baseValue,
			letterMult,
			wordMult,
			effectiveValue
		});
	}

	const scoreAfterLetterMults = baseScore + letterMultiplierBonus;
	const totalScore = scoreAfterLetterMults * totalWordMultiplier;
	const wordMultiplierBonus = totalScore - scoreAfterLetterMults;

	return {
		baseScore,
		letterMultiplierBonus,
		wordMultiplierBonus,
		totalScore,
		letterDetails
	};
}

/**
 * Calculate the maximum possible score for a word on any board
 */
export function calculateMaxPossibleScore(word: Word): number {
	// Assume maximum multipliers (3x letter, 3x word for each position)
	let maxScore = 0;
	const maxWordMultiplier = Math.pow(3, word.length);

	for (const letter of word.letters) {
		const baseValue = LETTER_SCORES[letter.toUpperCase()] || 0;
		maxScore += baseValue * 3; // Maximum letter multiplier
	}

	return maxScore * maxWordMultiplier;
}

/**
 * Calculate scoring efficiency (actual score / max possible score)
 */
export function calculateScoringEfficiency(word: Word, board: Board): number {
	const actualScore = calculateScoringBreakdown(word, board).totalScore;
	const maxScore = calculateMaxPossibleScore(word);
	return maxScore > 0 ? actualScore / maxScore : 0;
}

/**
 * Find the best scoring positions for a word on the board
 */
export function findBestScoringPositions(
	letters: string,
	board: Board,
	maxResults = 10
): Array<{ positions: [number, number][]; score: number }> {
	const results: Array<{ positions: [number, number][]; score: number }> = [];
	const wordLength = letters.length;

	// Try all possible starting positions and directions
	for (let startRow = 0; startRow < 5; startRow++) {
		for (let startCol = 0; startCol < 5; startCol++) {
			// Try horizontal placement
			if (startCol + wordLength <= 5) {
				const positions: [number, number][] = [];
				for (let i = 0; i < wordLength; i++) {
					positions.push([startRow, startCol + i]);
				}

				if (isValidPlacement(positions, board)) {
					const word = new Word(letters, positions);
					const score = calculateScoringBreakdown(word, board).totalScore;
					results.push({ positions, score });
				}
			}

			// Try vertical placement
			if (startRow + wordLength <= 5) {
				const positions: [number, number][] = [];
				for (let i = 0; i < wordLength; i++) {
					positions.push([startRow + i, startCol]);
				}

				if (isValidPlacement(positions, board)) {
					const word = new Word(letters, positions);
					const score = calculateScoringBreakdown(word, board).totalScore;
					results.push({ positions, score });
				}
			}
		}
	}

	// Sort by score descending and return top results
	return results.sort((a, b) => b.score - a.score).slice(0, maxResults);
}

/**
 * Check if a placement is valid (letters match board tiles)
 */
function isValidPlacement(positions: [number, number][], board: Board): boolean {
	for (const [row, col] of positions) {
		const tile = board.getTile(row, col);
		if (!tile) return false;
	}
	return true;
}

/**
 * Calculate bonus points for using high-value tiles
 */
export function calculateHighValueBonus(word: Word): number {
	let bonus = 0;
	const highValueLetters = ['Q', 'X', 'Z', 'J'];

	for (const letter of word.letters) {
		if (highValueLetters.includes(letter.toUpperCase())) {
			bonus += 5; // Bonus for using high-value letters
		}
	}

	return bonus;
}

/**
 * Calculate bonus points for word length
 */
export function calculateLengthBonus(word: Word): number {
	if (word.length >= 7) return 50;
	if (word.length >= 6) return 25;
	if (word.length >= 5) return 10;
	return 0;
}

/**
 * Calculate total score including all bonuses
 */
export function calculateTotalScore(word: Word, board: Board): number {
	const breakdown = calculateScoringBreakdown(word, board);
	const highValueBonus = calculateHighValueBonus(word);
	const lengthBonus = calculateLengthBonus(word);

	return breakdown.totalScore + highValueBonus + lengthBonus;
}

/**
 * Analyze scoring potential of a board position
 */
export function analyzePositionValue(
	row: number,
	col: number,
	board: Board
): {
	letterMultiplier: number;
	wordMultiplier: number;
	adjacentMultipliers: number;
	strategicValue: number;
} {
	const tile = board.getTile(row, col);
	if (!tile) {
		return { letterMultiplier: 1, wordMultiplier: 1, adjacentMultipliers: 0, strategicValue: 0 };
	}

	// Count adjacent multipliers
	const adjacent = board.getAdjacentTiles(row, col);
	const adjacentMultipliers = adjacent.reduce((sum, adjTile) => {
		return sum + (adjTile.letterMult - 1) + (adjTile.wordMult - 1);
	}, 0);

	// Calculate strategic value (center positions are more valuable)
	const centerDistance = Math.abs(row - 2) + Math.abs(col - 2);
	const strategicValue = Math.max(0, 5 - centerDistance);

	return {
		letterMultiplier: tile.letterMult,
		wordMultiplier: tile.wordMult,
		adjacentMultipliers,
		strategicValue
	};
}

/**
 * Get scoring statistics for a set of words
 */
export function getWordSetStats(words: Word[], board: Board) {
	if (words.length === 0) {
		return {
			totalScore: 0,
			averageScore: 0,
			highestScore: 0,
			lowestScore: 0,
			scoreDistribution: []
		};
	}

	const scores = words.map((word) => calculateTotalScore(word, board));
	const totalScore = scores.reduce((sum, score) => sum + score, 0);
	const averageScore = totalScore / scores.length;
	const highestScore = Math.max(...scores);
	const lowestScore = Math.min(...scores);

	// Create score distribution (buckets of 10)
	const scoreDistribution: { range: string; count: number }[] = [];
	const maxBucket = Math.ceil(highestScore / 10);

	for (let i = 0; i <= maxBucket; i++) {
		const rangeStart = i * 10;
		const rangeEnd = (i + 1) * 10 - 1;
		const count = scores.filter((score) => score >= rangeStart && score <= rangeEnd).length;

		if (count > 0) {
			scoreDistribution.push({
				range: `${rangeStart}-${rangeEnd}`,
				count
			});
		}
	}

	return {
		totalScore,
		averageScore,
		highestScore,
		lowestScore,
		scoreDistribution
	};
}

/**
 * Compare two words' scoring potential
 */
export function compareWords(
	word1: Word,
	word2: Word,
	board: Board
): {
	word1Score: number;
	word2Score: number;
	difference: number;
	betterWord: 'word1' | 'word2' | 'tie';
	analysis: string;
} {
	const score1 = calculateTotalScore(word1, board);
	const score2 = calculateTotalScore(word2, board);
	const difference = Math.abs(score1 - score2);

	let betterWord: 'word1' | 'word2' | 'tie';
	let analysis: string;

	if (score1 > score2) {
		betterWord = 'word1';
		analysis = `Word 1 (${word1.letters}) scores ${difference} points higher than Word 2 (${word2.letters})`;
	} else if (score2 > score1) {
		betterWord = 'word2';
		analysis = `Word 2 (${word2.letters}) scores ${difference} points higher than Word 1 (${word1.letters})`;
	} else {
		betterWord = 'tie';
		analysis = `Both words score equally (${score1} points)`;
	}

	return {
		word1Score: score1,
		word2Score: score2,
		difference,
		betterWord,
		analysis
	};
}
