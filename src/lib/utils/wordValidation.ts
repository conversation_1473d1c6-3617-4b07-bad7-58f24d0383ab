/**
 * Word validation utilities for the Letters game solver
 * 
 * This module provides high-level functions for validating words
 * and calculating scores during the solving process.
 */

import { getDictionary, type DictionaryEntry } from './dictionary';
import { LETTER_SCORES, type Word, type Tile } from '../types';

/**
 * Validate if a word is in the dictionary
 */
export function isValidWord(word: string): boolean {
	try {
		return getDictionary().hasWord(word);
	} catch (error) {
		console.warn('Dictionary not loaded, cannot validate word:', word);
		return false;
	}
}

/**
 * Get the base score for a word (sum of letter values without multipliers)
 */
export function getWordBaseScore(word: string): number {
	try {
		return getDictionary().getBaseScore(word);
	} catch (error) {
		// Fallback calculation if dictionary not available
		let score = 0;
		for (const char of word.toUpperCase()) {
			score += LETTER_SCORES[char] || 0;
		}
		return score;
	}
}

/**
 * Calculate the full score for a word on the board including multipliers
 */
export function calculateWordScore(word: Word, tiles: Tile[][]): number {
	let totalScore = 0;
	let wordMultiplier = 1;

	// Calculate score for each letter
	for (let i = 0; i < word.positions.length; i++) {
		const [row, col] = word.positions[i];
		const tile = tiles[row][col];
		const letter = word.letters[i];

		// Get base letter score
		const letterScore = LETTER_SCORES[letter.toUpperCase()] || 0;

		// Apply letter multiplier
		const multipliedLetterScore = letterScore * tile.letterMult;
		totalScore += multipliedLetterScore;

		// Accumulate word multiplier
		wordMultiplier *= tile.wordMult;
	}

	// Apply word multiplier
	totalScore *= wordMultiplier;

	return totalScore;
}

/**
 * Find all valid words that can be formed from available tiles
 */
export function findValidWords(availableTiles: Tile[], maxWords = 100): DictionaryEntry[] {
	try {
		const dictionary = getDictionary();
		const letters = availableTiles.map(tile => tile.letter);
		const possibleWords = dictionary.findPossibleWords(letters);
		
		// Sort by base score descending and limit results
		return possibleWords
			.sort((a, b) => b.baseScore - a.baseScore)
			.slice(0, maxWords);
	} catch (error) {
		console.warn('Dictionary not loaded, cannot find valid words');
		return [];
	}
}

/**
 * Check if a sequence of positions forms a valid word path
 * (adjacent tiles, no repeats, etc.)
 */
export function isValidWordPath(positions: [number, number][]): boolean {
	if (positions.length < 2) return false;

	const used = new Set<string>();

	for (let i = 0; i < positions.length; i++) {
		const [row, col] = positions[i];

		// Check bounds
		if (row < 0 || row >= 5 || col < 0 || col >= 5) {
			return false;
		}

		// Check for duplicates
		const key = `${row},${col}`;
		if (used.has(key)) {
			return false;
		}
		used.add(key);

		// Check adjacency (except for first position)
		if (i > 0) {
			const [prevRow, prevCol] = positions[i - 1];
			const rowDiff = Math.abs(row - prevRow);
			const colDiff = Math.abs(col - prevCol);

			// Must be adjacent (including diagonally)
			if (rowDiff > 1 || colDiff > 1 || (rowDiff === 0 && colDiff === 0)) {
				return false;
			}
		}
	}

	return true;
}

/**
 * Create a Word object from positions and board state
 */
export function createWordFromPositions(
	positions: [number, number][],
	tiles: Tile[][]
): Word | null {
	if (!isValidWordPath(positions)) {
		return null;
	}

	// Extract letters
	const letters = positions.map(([row, col]) => tiles[row][col].letter).join('');

	// Validate word exists in dictionary
	if (!isValidWord(letters)) {
		return null;
	}

	// Calculate score
	const word: Word = {
		letters,
		positions,
		score: 0,
		value: letters
	};

	word.score = calculateWordScore(word, tiles);

	return word;
}

/**
 * Get word statistics for debugging/analysis
 */
export function getWordStats(word: string) {
	const entry = getDictionary().getEntry(word);
	if (!entry) {
		return null;
	}

	return {
		word: entry.word,
		length: entry.wordLength,
		baseScore: entry.baseScore,
		letterFrequency: Array.from(entry.letterHistogram),
		averageLetterScore: entry.baseScore / entry.wordLength
	};
}
