/**
 * Dictionary loading and parsing utilities
 * 
 * This module handles loading the compiled binary dictionary and provides
 * functions for word validation and scoring.
 */

import { LETTER_SCORES } from '../types';

/**
 * Represents a dictionary entry with precomputed metadata
 */
export interface DictionaryEntry {
	/** The word in uppercase */
	word: string;
	/** UTF-8 byte length */
	byteLength: number;
	/** Sum of letter scores (no multipliers) */
	baseScore: number;
	/** Character length */
	wordLength: number;
	/** Letter frequency histogram A-Z */
	letterHistogram: Uint8Array;
}

/**
 * In-memory dictionary for fast lookups
 */
export class Dictionary {
	private entries: Map<string, DictionaryEntry> = new Map();
	private loaded = false;

	/**
	 * Load dictionary from compiled binary file
	 */
	async loadFromBinary(binaryData: ArrayBuffer): Promise<void> {
		const view = new DataView(binaryData);
		let offset = 0;

		// Read header: uint32 nEntries (little-endian)
		const entryCount = view.getUint32(offset, true);
		offset += 4;

		console.log(`[Dictionary] Loading ${entryCount} entries...`);

		// Read each entry
		for (let i = 0; i < entryCount; i++) {
			// Read record header
			const wordByteLen = view.getUint16(offset, true);
			offset += 2;

			const letterScoreSum = view.getUint16(offset, true);
			offset += 2;

			const wordLen = view.getUint8(offset);
			offset += 1;

			// Read 26-byte histogram
			const hist = new Uint8Array(26);
			for (let j = 0; j < 26; j++) {
				hist[j] = view.getUint8(offset);
				offset += 1;
			}

			// Read word bytes
			const wordBytes = new Uint8Array(binaryData, offset, wordByteLen);
			const word = new TextDecoder().decode(wordBytes);
			offset += wordByteLen;

			// Store entry
			const entry: DictionaryEntry = {
				word,
				byteLength: wordByteLen,
				baseScore: letterScoreSum,
				wordLength: wordLen,
				letterHistogram: hist
			};

			this.entries.set(word, entry);
		}

		this.loaded = true;
		console.log(`[Dictionary] Loaded ${this.entries.size} words successfully`);
	}

	/**
	 * Check if a word exists in the dictionary
	 */
	hasWord(word: string): boolean {
		if (!this.loaded) {
			throw new Error('Dictionary not loaded');
		}
		return this.entries.has(word.toUpperCase());
	}

	/**
	 * Get dictionary entry for a word
	 */
	getEntry(word: string): DictionaryEntry | undefined {
		if (!this.loaded) {
			throw new Error('Dictionary not loaded');
		}
		return this.entries.get(word.toUpperCase());
	}

	/**
	 * Get base score for a word (sum of letter values, no multipliers)
	 */
	getBaseScore(word: string): number {
		const entry = this.getEntry(word);
		if (entry) {
			return entry.baseScore;
		}

		// Calculate on-the-fly if not in dictionary
		let score = 0;
		for (const char of word.toUpperCase()) {
			score += LETTER_SCORES[char] || 0;
		}
		return score;
	}

	/**
	 * Get all words that can be formed with given letters
	 * @param availableLetters - Array of available letters
	 * @param maxLength - Maximum word length to consider
	 */
	findPossibleWords(availableLetters: string[], maxLength = 25): DictionaryEntry[] {
		if (!this.loaded) {
			throw new Error('Dictionary not loaded');
		}

		// Create histogram of available letters
		const available = new Uint8Array(26);
		for (const letter of availableLetters) {
			const idx = letter.toUpperCase().charCodeAt(0) - 65;
			if (idx >= 0 && idx < 26) {
				available[idx]++;
			}
		}

		const results: DictionaryEntry[] = [];

		// Check each dictionary entry
		for (const entry of this.entries.values()) {
			if (entry.wordLength > maxLength) continue;

			// Check if we have enough letters
			let canForm = true;
			for (let i = 0; i < 26; i++) {
				if (entry.letterHistogram[i] > available[i]) {
					canForm = false;
					break;
				}
			}

			if (canForm) {
				results.push(entry);
			}
		}

		return results;
	}

	/**
	 * Get dictionary statistics
	 */
	getStats() {
		return {
			totalWords: this.entries.size,
			loaded: this.loaded
		};
	}
}

// Global dictionary instance
let globalDictionary: Dictionary | null = null;

/**
 * Load the global dictionary from binary file
 */
export async function loadDictionary(binaryData: ArrayBuffer): Promise<Dictionary> {
	if (!globalDictionary) {
		globalDictionary = new Dictionary();
	}
	
	if (!globalDictionary.getStats().loaded) {
		await globalDictionary.loadFromBinary(binaryData);
	}
	
	return globalDictionary;
}

/**
 * Get the global dictionary instance
 */
export function getDictionary(): Dictionary {
	if (!globalDictionary) {
		throw new Error('Dictionary not initialized. Call loadDictionary() first.');
	}
	return globalDictionary;
}

/**
 * Validate if a word exists in the dictionary
 */
export function validateWord(word: string): boolean {
	return getDictionary().hasWord(word);
}

/**
 * Calculate base score for a word
 */
export function calculateBaseScore(word: string): number {
	return getDictionary().getBaseScore(word);
}
