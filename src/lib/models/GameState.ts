/**
 * GameState class implementation for the Letters game
 * 
 * Represents the complete state of a game including board, turn number,
 * total score, and move history.
 */

import { Board } from './Board';
import { Word } from './Word';
import { GAME_CONFIG, type GameState as GameStateInterface } from '../types';

/**
 * Represents the complete game state
 */
export class GameState implements GameStateInterface {
	public readonly board: Board;
	public readonly turn: 0 | 1 | 2 | 3 | 4 | 5;
	public readonly total: number;
	public readonly moves: Word[];

	constructor(
		board: Board,
		turn: 0 | 1 | 2 | 3 | 4 | 5 = 0,
		total: number = 0,
		moves: Word[] = []
	) {
		this.board = board;
		this.turn = turn;
		this.total = total;
		this.moves = [...moves]; // Create a copy to prevent mutation

		// Validate turn number
		if (turn < 0 || turn > GAME_CONFIG.MAX_TURNS) {
			throw new Error(`Turn must be between 0 and ${GAME_CONFIG.MAX_TURNS}, got ${turn}`);
		}

		// Validate that moves count matches turn number
		if (this.moves.length !== this.turn) {
			throw new Error(`Number of moves (${this.moves.length}) must match turn number (${this.turn})`);
		}
	}

	/**
	 * Check if the game is complete
	 */
	isGameComplete(): boolean {
		return this.turn >= GAME_CONFIG.MAX_TURNS;
	}

	/**
	 * Check if the game can accept more moves
	 */
	canPlayMove(): boolean {
		return this.turn < GAME_CONFIG.MAX_TURNS;
	}

	/**
	 * Get the number of remaining turns
	 */
	getRemainingTurns(): number {
		return Math.max(0, GAME_CONFIG.MAX_TURNS - this.turn);
	}

	/**
	 * Play a move and return a new GameState
	 */
	playMove(word: Word): GameState {
		if (!this.canPlayMove()) {
			throw new Error('Cannot play move: game is complete');
		}

		// Calculate the score for this word on the current board
		const wordScore = word.calculateScore(this.board);
		const scoredWord = new Word(word.letters, word.positions, wordScore);

		// Apply the word to the board to get the new board state
		const newBoard = this.board.apply(scoredWord);

		// Create new game state
		return new GameState(
			newBoard,
			(this.turn + 1) as 0 | 1 | 2 | 3 | 4 | 5,
			this.total + wordScore,
			[...this.moves, scoredWord]
		);
	}

	/**
	 * Get the last move played
	 */
	getLastMove(): Word | null {
		return this.moves.length > 0 ? this.moves[this.moves.length - 1] : null;
	}

	/**
	 * Get move by turn number (0-indexed)
	 */
	getMove(turnNumber: number): Word | null {
		if (turnNumber < 0 || turnNumber >= this.moves.length) {
			return null;
		}
		return this.moves[turnNumber];
	}

	/**
	 * Get all moves as a copy
	 */
	getAllMoves(): Word[] {
		return [...this.moves];
	}

	/**
	 * Get the average score per move
	 */
	getAverageScore(): number {
		return this.moves.length > 0 ? this.total / this.moves.length : 0;
	}

	/**
	 * Get the highest scoring move
	 */
	getHighestScoringMove(): Word | null {
		if (this.moves.length === 0) return null;
		
		return this.moves.reduce((highest, current) => 
			current.score > highest.score ? current : highest
		);
	}

	/**
	 * Get the lowest scoring move
	 */
	getLowestScoringMove(): Word | null {
		if (this.moves.length === 0) return null;
		
		return this.moves.reduce((lowest, current) => 
			current.score < lowest.score ? current : lowest
		);
	}

	/**
	 * Get game statistics
	 */
	getStats() {
		return {
			turn: this.turn,
			total: this.total,
			movesPlayed: this.moves.length,
			remainingTurns: this.getRemainingTurns(),
			averageScore: this.getAverageScore(),
			isComplete: this.isGameComplete(),
			highestMove: this.getHighestScoringMove(),
			lowestMove: this.getLowestScoringMove()
		};
	}

	/**
	 * Get a summary of all moves
	 */
	getMoveSummary(): Array<{ turn: number; word: string; score: number; positions: string }> {
		return this.moves.map((move, index) => ({
			turn: index + 1,
			word: move.letters,
			score: move.score,
			positions: move.positions.map(([r, c]) => `[${r},${c}]`).join('-')
		}));
	}

	/**
	 * Check if a word would be valid to play in the current state
	 */
	canPlayWord(word: Word): boolean {
		if (!this.canPlayMove()) return false;
		if (!word.isValid()) return false;
		if (!word.hasValidPath()) return false;
		
		// Check that all positions exist on the board
		for (const [row, col] of word.positions) {
			if (!this.board.getTile(row, col)) return false;
		}
		
		return true;
	}

	/**
	 * Simulate playing a word without modifying this state
	 */
	simulateMove(word: Word): GameState | null {
		if (!this.canPlayWord(word)) return null;
		return this.playMove(word);
	}

	/**
	 * Create a deep copy of this game state
	 */
	clone(): GameState {
		return new GameState(
			this.board.clone(),
			this.turn,
			this.total,
			this.moves.map(move => move.clone())
		);
	}

	/**
	 * Convert to plain object for serialization
	 */
	toObject(): GameStateInterface {
		return {
			board: this.board.toObject(),
			turn: this.turn,
			total: this.total,
			moves: this.moves.map(move => move.toObject())
		};
	}

	/**
	 * Create GameState from plain object
	 */
	static fromObject(obj: GameStateInterface): GameState {
		const board = Board.fromObject(obj.board);
		const moves = obj.moves.map(moveObj => Word.fromObject(moveObj));
		return new GameState(board, obj.turn, obj.total, moves);
	}

	/**
	 * Create a new game with a random board
	 */
	static createNew(board?: Board): GameState {
		const gameBoard = board || Board.createRandom();
		return new GameState(gameBoard);
	}

	/**
	 * Create a game state from an existing board at a specific turn
	 */
	static fromBoard(board: Board, turn: 0 | 1 | 2 | 3 | 4 | 5 = 0): GameState {
		return new GameState(board, turn);
	}

	/**
	 * String representation for debugging
	 */
	toString(): string {
		const movesSummary = this.moves.map((move, i) => 
			`${i + 1}. ${move.letters} (${move.score})`
		).join(', ');
		
		return `GameState[Turn ${this.turn}/${GAME_CONFIG.MAX_TURNS}, Total: ${this.total}, Moves: ${movesSummary}]`;
	}

	/**
	 * Detailed string representation
	 */
	toDetailedString(): string {
		let result = `=== Game State ===\n`;
		result += `Turn: ${this.turn}/${GAME_CONFIG.MAX_TURNS}\n`;
		result += `Total Score: ${this.total}\n`;
		result += `Moves Played: ${this.moves.length}\n`;
		result += `Game Complete: ${this.isGameComplete()}\n\n`;
		
		if (this.moves.length > 0) {
			result += `Move History:\n`;
			this.moves.forEach((move, i) => {
				result += `  ${i + 1}. ${move.toString()}\n`;
			});
			result += '\n';
		}
		
		result += `Current Board:\n${this.board.toString()}`;
		
		return result;
	}

	/**
	 * Check equality with another game state
	 */
	equals(other: GameState): boolean {
		if (this.turn !== other.turn) return false;
		if (this.total !== other.total) return false;
		if (this.moves.length !== other.moves.length) return false;
		
		// Check moves equality
		for (let i = 0; i < this.moves.length; i++) {
			if (!this.moves[i].equals(other.moves[i])) return false;
		}
		
		// Note: We don't check board equality as it's derived from moves
		return true;
	}
}
