/**
 * Tile class implementation for the Letters game
 * 
 * Represents a single tile on the game board with letter, scoring,
 * and multiplier information.
 */

import { LETTER_SCORES, type Tile as TileInterface } from '../types';

/**
 * Represents a single tile on the game board
 */
export class Tile implements TileInterface {
	public readonly letter: string;
	public readonly base: number;
	public readonly letterMult: 1 | 2 | 3;
	public readonly wordMult: 1 | 2 | 3;
	public readonly col: 0 | 1 | 2 | 3 | 4;
	public readonly row: 0 | 1 | 2 | 3 | 4;

	constructor(
		letter: string,
		row: 0 | 1 | 2 | 3 | 4,
		col: 0 | 1 | 2 | 3 | 4,
		letterMult: 1 | 2 | 3 = 1,
		wordMult: 1 | 2 | 3 = 1
	) {
		this.letter = letter.toUpperCase();
		this.row = row;
		this.col = col;
		this.letterMult = letterMult;
		this.wordMult = wordMult;
		this.base = LETTER_SCORES[this.letter] || 0;
	}

	/**
	 * Get the effective letter score including letter multiplier
	 */
	getLetterScore(): number {
		return this.base * this.letterMult;
	}

	/**
	 * Check if this tile has any multipliers
	 */
	hasMultipliers(): boolean {
		return this.letterMult > 1 || this.wordMult > 1;
	}

	/**
	 * Get a string representation of the tile position
	 */
	getPositionKey(): string {
		return `${this.row},${this.col}`;
	}

	/**
	 * Check if this tile is adjacent to another tile (including diagonally)
	 */
	isAdjacentTo(other: Tile): boolean {
		const rowDiff = Math.abs(this.row - other.row);
		const colDiff = Math.abs(this.col - other.col);
		
		// Adjacent means within 1 step in any direction (including diagonal)
		// but not the same position
		return (rowDiff <= 1 && colDiff <= 1) && !(rowDiff === 0 && colDiff === 0);
	}

	/**
	 * Calculate Manhattan distance to another tile
	 */
	manhattanDistanceTo(other: Tile): number {
		return Math.abs(this.row - other.row) + Math.abs(this.col - other.col);
	}

	/**
	 * Calculate Euclidean distance to another tile
	 */
	euclideanDistanceTo(other: Tile): number {
		const rowDiff = this.row - other.row;
		const colDiff = this.col - other.col;
		return Math.sqrt(rowDiff * rowDiff + colDiff * colDiff);
	}

	/**
	 * Create a copy of this tile with different multipliers
	 */
	withMultipliers(letterMult: 1 | 2 | 3, wordMult: 1 | 2 | 3): Tile {
		return new Tile(this.letter, this.row, this.col, letterMult, wordMult);
	}

	/**
	 * Create a copy of this tile with a different letter
	 */
	withLetter(letter: string): Tile {
		return new Tile(letter, this.row, this.col, this.letterMult, this.wordMult);
	}

	/**
	 * Create a copy of this tile at a different position
	 */
	withPosition(row: 0 | 1 | 2 | 3 | 4, col: 0 | 1 | 2 | 3 | 4): Tile {
		return new Tile(this.letter, row, col, this.letterMult, this.wordMult);
	}

	/**
	 * Convert to plain object (for serialization)
	 */
	toObject(): TileInterface {
		return {
			letter: this.letter,
			base: this.base,
			letterMult: this.letterMult,
			wordMult: this.wordMult,
			col: this.col,
			row: this.row
		};
	}

	/**
	 * Create a Tile from a plain object
	 */
	static fromObject(obj: TileInterface): Tile {
		return new Tile(obj.letter, obj.row, obj.col, obj.letterMult, obj.wordMult);
	}

	/**
	 * Create a tile with random letter and position
	 */
	static createRandom(row: 0 | 1 | 2 | 3 | 4, col: 0 | 1 | 2 | 3 | 4): Tile {
		// Common letters weighted by frequency in English
		const letters = 'AAAAAAAAABBCCDDDDEEEEEEEEEEEEFFGGGHHIIIIIIIIIJKLLLLMMNNNNNNOOOOOOOOPPQRRRRRRSSSSTTTTTTUUUUVVWWXYYZ';
		const randomLetter = letters[Math.floor(Math.random() * letters.length)];
		
		// Occasionally add multipliers (simplified for testing)
		const letterMult = Math.random() < 0.1 ? (Math.random() < 0.5 ? 2 : 3) : 1;
		const wordMult = Math.random() < 0.1 ? (Math.random() < 0.5 ? 2 : 3) : 1;
		
		return new Tile(randomLetter, row, col, letterMult as 1 | 2 | 3, wordMult as 1 | 2 | 3);
	}

	/**
	 * String representation for debugging
	 */
	toString(): string {
		const multipliers = this.hasMultipliers() 
			? ` (L${this.letterMult}×W${this.wordMult}×)` 
			: '';
		return `${this.letter}[${this.row},${this.col}]${multipliers}`;
	}

	/**
	 * Check equality with another tile
	 */
	equals(other: Tile): boolean {
		return (
			this.letter === other.letter &&
			this.row === other.row &&
			this.col === other.col &&
			this.letterMult === other.letterMult &&
			this.wordMult === other.wordMult
		);
	}

	/**
	 * Create a deep copy of this tile
	 */
	clone(): Tile {
		return new Tile(this.letter, this.row, this.col, this.letterMult, this.wordMult);
	}
}
