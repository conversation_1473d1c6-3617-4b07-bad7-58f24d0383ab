/**
 * Word class implementation for the Letters game
 * 
 * Represents a word that can be played on the board with its positions,
 * letters, and calculated score.
 */

import { type Word as WordInterface, type Position } from '../types';
import { isValidWord, calculateWordScore } from '../utils/wordValidation';
import type { Board } from './Board';

/**
 * Represents a word that can be played on the board
 */
export class Word implements WordInterface {
	public readonly letters: string;
	public readonly positions: Position[];
	public readonly score: number;
	public readonly value: string;

	constructor(letters: string, positions: Position[], score?: number) {
		this.letters = letters.toUpperCase();
		this.value = this.letters; // For API compatibility
		this.positions = [...positions]; // Create a copy to prevent mutation
		
		// Validate that positions match letters length
		if (this.letters.length !== this.positions.length) {
			throw new Error(`Letters length (${this.letters.length}) must match positions length (${this.positions.length})`);
		}

		// Score can be provided or will be calculated later when board is available
		this.score = score ?? 0;
	}

	/**
	 * Calculate the score for this word on a given board
	 */
	calculateScore(board: Board): number {
		return calculateWordScore(this, board.tiles);
	}

	/**
	 * Create a new Word with the calculated score
	 */
	withScore(board: Board): Word {
		const calculatedScore = this.calculateScore(board);
		return new Word(this.letters, this.positions, calculatedScore);
	}

	/**
	 * Check if this word is valid in the dictionary
	 */
	isValid(): boolean {
		return isValidWord(this.letters);
	}

	/**
	 * Get the length of the word
	 */
	get length(): number {
		return this.letters.length;
	}

	/**
	 * Get the first position
	 */
	get startPosition(): Position {
		return this.positions[0];
	}

	/**
	 * Get the last position
	 */
	get endPosition(): Position {
		return this.positions[this.positions.length - 1];
	}

	/**
	 * Check if this word uses a specific position
	 */
	usesPosition(row: number, col: number): boolean {
		return this.positions.some(([r, c]) => r === row && c === col);
	}

	/**
	 * Check if this word overlaps with another word (shares any positions)
	 */
	overlapsWith(other: Word): boolean {
		const thisPositions = new Set(this.positions.map(([r, c]) => `${r},${c}`));
		return other.positions.some(([r, c]) => thisPositions.has(`${r},${c}`));
	}

	/**
	 * Get positions as a set of strings for efficient lookup
	 */
	getPositionSet(): Set<string> {
		return new Set(this.positions.map(([r, c]) => `${r},${c}`));
	}

	/**
	 * Check if the word path is valid (adjacent positions, no duplicates)
	 */
	hasValidPath(): boolean {
		if (this.positions.length < 2) return true;

		// Check for duplicate positions
		const positionSet = new Set();
		for (const [row, col] of this.positions) {
			const key = `${row},${col}`;
			if (positionSet.has(key)) {
				return false; // Duplicate position
			}
			positionSet.add(key);
		}

		// Check adjacency
		for (let i = 1; i < this.positions.length; i++) {
			const [prevRow, prevCol] = this.positions[i - 1];
			const [currRow, currCol] = this.positions[i];
			
			const rowDiff = Math.abs(currRow - prevRow);
			const colDiff = Math.abs(currCol - prevCol);
			
			// Must be adjacent (including diagonally)
			if (rowDiff > 1 || colDiff > 1 || (rowDiff === 0 && colDiff === 0)) {
				return false;
			}
		}

		return true;
	}

	/**
	 * Get the bounding box of this word's positions
	 */
	getBoundingBox(): { minRow: number; maxRow: number; minCol: number; maxCol: number } {
		if (this.positions.length === 0) {
			return { minRow: 0, maxRow: 0, minCol: 0, maxCol: 0 };
		}

		let minRow = this.positions[0][0];
		let maxRow = this.positions[0][0];
		let minCol = this.positions[0][1];
		let maxCol = this.positions[0][1];

		for (const [row, col] of this.positions) {
			minRow = Math.min(minRow, row);
			maxRow = Math.max(maxRow, row);
			minCol = Math.min(minCol, col);
			maxCol = Math.max(maxCol, col);
		}

		return { minRow, maxRow, minCol, maxCol };
	}

	/**
	 * Check if this word is horizontal (same row)
	 */
	isHorizontal(): boolean {
		if (this.positions.length <= 1) return true;
		const firstRow = this.positions[0][0];
		return this.positions.every(([row]) => row === firstRow);
	}

	/**
	 * Check if this word is vertical (same column)
	 */
	isVertical(): boolean {
		if (this.positions.length <= 1) return true;
		const firstCol = this.positions[0][1];
		return this.positions.every(([, col]) => col === firstCol);
	}

	/**
	 * Check if this word is diagonal
	 */
	isDiagonal(): boolean {
		return !this.isHorizontal() && !this.isVertical() && this.hasValidPath();
	}

	/**
	 * Create a reversed version of this word
	 */
	reverse(): Word {
		const reversedLetters = this.letters.split('').reverse().join('');
		const reversedPositions = [...this.positions].reverse();
		return new Word(reversedLetters, reversedPositions, this.score);
	}

	/**
	 * Convert to plain object for serialization
	 */
	toObject(): WordInterface {
		return {
			letters: this.letters,
			positions: this.positions,
			score: this.score,
			value: this.value
		};
	}

	/**
	 * Create Word from plain object
	 */
	static fromObject(obj: WordInterface): Word {
		return new Word(obj.letters, obj.positions, obj.score);
	}

	/**
	 * Create a word from a string and starting position (horizontal)
	 */
	static createHorizontal(letters: string, startRow: number, startCol: number): Word {
		const positions: Position[] = [];
		for (let i = 0; i < letters.length; i++) {
			positions.push([startRow, startCol + i]);
		}
		return new Word(letters, positions);
	}

	/**
	 * Create a word from a string and starting position (vertical)
	 */
	static createVertical(letters: string, startRow: number, startCol: number): Word {
		const positions: Position[] = [];
		for (let i = 0; i < letters.length; i++) {
			positions.push([startRow + i, startCol]);
		}
		return new Word(letters, positions);
	}

	/**
	 * Create a word from positions and extract letters from board
	 */
	static fromPositions(positions: Position[], board: Board): Word | null {
		if (positions.length === 0) return null;

		const letters: string[] = [];
		for (const [row, col] of positions) {
			const tile = board.getTile(row, col);
			if (!tile) return null;
			letters.push(tile.letter);
		}

		const word = new Word(letters.join(''), positions);
		return word.hasValidPath() ? word.withScore(board) : null;
	}

	/**
	 * String representation for debugging
	 */
	toString(): string {
		const posStr = this.positions.map(([r, c]) => `[${r},${c}]`).join('-');
		return `${this.letters} (${posStr}) = ${this.score}`;
	}

	/**
	 * Check equality with another word
	 */
	equals(other: Word): boolean {
		if (this.letters !== other.letters) return false;
		if (this.positions.length !== other.positions.length) return false;
		
		for (let i = 0; i < this.positions.length; i++) {
			const [thisRow, thisCol] = this.positions[i];
			const [otherRow, otherCol] = other.positions[i];
			if (thisRow !== otherRow || thisCol !== otherCol) return false;
		}
		
		return true;
	}

	/**
	 * Create a deep copy of this word
	 */
	clone(): Word {
		return new Word(this.letters, this.positions, this.score);
	}
}
