/**
 * Solver module exports
 *
 * This module contains the core solving algorithm and related utilities
 * for finding optimal word sequences in the Letters game.
 */

// Scraping utilities
export { scrapeBoard, injectHelperFunctions, createTestBoard } from './scraping';

// Game interaction utilities
export {
	playWord,
	undoLastMove,
	undoToState,
	getCurrentWord,
	isGameReady,
	waitForGameReady
} from './gameInteraction';

// Hashing utilities
export {
	hashBoard,
	hashGameState,
	BoardHashMap,
	BoardHashSet,
	benchmarkHashingAlgorithms,
	analyzeHashDistribution
} from './hashing';

// Main solver function
export {
	solveDailyBoard,
	solveDailyBoardWithExecution,
	getSolverStats,
	benchmarkSolver
} from './solveDailyBoard';

// Optimization utilities
export {
	upperBoundForBoard,
	upperBoundForGameState,
	shouldPruneNode,
	calculateTightenedUpperBound,
	clearUpperBoundCache,
	getUpperBoundCacheStats,
	benchmarkUpperBoundCalculation
} from './optimization';

// Hungarian algorithm
export {
	Hungarian,
	solveAssignment,
	createTileWordCostMatrix,
	benchmarkHungarian,
	type HungarianResult
} from './hungarian';

// Parameter optimization
export {
	optimizeParameters,
	findOptimalParameters,
	analyzeParameterSensitivity,
	generateParameterRecommendations,
	validateParameters,
	OPTIMIZED_PARAMETERS,
	type ParameterSet
} from './parameterOptimization';

// Error handling
export {
	withErrorHandling,
	safeScrapeBoard,
	safePlayWord,
	safeUndoLastMove,
	validateBoardState,
	CircuitBreaker,
	getErrorStats,
	resetErrorStats,
	createFallbackResult,
	SolverError,
	SolverErrorType
} from './errorHandling';
