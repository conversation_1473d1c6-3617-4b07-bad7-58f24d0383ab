/**
 * Error handling utilities for the solver
 * 
 * This module provides robust error handling, recovery mechanisms,
 * and fallback strategies for browser failures and edge cases.
 */

import type { Page } from '@playwright/test';
import { scrapeBoard, playWord, undoLastMove } from './index';
import type { Board } from '../models/Board';
import type { BestLineResult } from '../types';

/**
 * Error types that can occur during solving
 */
export enum SolverErrorType {
	BROWSER_CRASH = 'browser_crash',
	NETWORK_TIMEOUT = 'network_timeout',
	SCRAPING_FAILED = 'scraping_failed',
	WORD_PLAY_FAILED = 'word_play_failed',
	UNDO_FAILED = 'undo_failed',
	INVALID_BOARD_STATE = 'invalid_board_state',
	TIMEOUT = 'timeout',
	MEMORY_EXHAUSTED = 'memory_exhausted',
	UNKNOWN = 'unknown'
}

/**
 * Solver error with context information
 */
export class SolverError extends Error {
	public readonly type: SolverErrorType;
	public readonly context: Record<string, any>;
	public readonly timestamp: Date;
	public readonly recoverable: boolean;

	constructor(
		type: SolverErrorType,
		message: string,
		context: Record<string, any> = {},
		recoverable: boolean = true
	) {
		super(message);
		this.name = 'SolverError';
		this.type = type;
		this.context = context;
		this.timestamp = new Date();
		this.recoverable = recoverable;
	}
}

/**
 * Error recovery configuration
 */
interface ErrorRecoveryConfig {
	/** Maximum retry attempts */
	maxRetries: number;
	/** Delay between retries (ms) */
	retryDelay: number;
	/** Enable fallback strategies */
	useFallbacks: boolean;
	/** Take screenshots on errors */
	captureScreenshots: boolean;
	/** Log detailed error information */
	verboseLogging: boolean;
}

/**
 * Default error recovery configuration
 */
const DEFAULT_RECOVERY_CONFIG: ErrorRecoveryConfig = {
	maxRetries: 3,
	retryDelay: 2000,
	useFallbacks: true,
	captureScreenshots: true,
	verboseLogging: true
};

/**
 * Error recovery statistics
 */
interface ErrorStats {
	totalErrors: number;
	errorsByType: Map<SolverErrorType, number>;
	recoveredErrors: number;
	unrecoverableErrors: number;
	averageRecoveryTime: number;
}

/**
 * Global error statistics
 */
const errorStats: ErrorStats = {
	totalErrors: 0,
	errorsByType: new Map(),
	recoveredErrors: 0,
	unrecoverableErrors: 0,
	averageRecoveryTime: 0
};

/**
 * Execute a function with error handling and recovery
 */
export async function withErrorHandling<T>(
	operation: () => Promise<T>,
	operationName: string,
	config: ErrorRecoveryConfig = DEFAULT_RECOVERY_CONFIG
): Promise<T> {
	let lastError: SolverError | null = null;
	const startTime = Date.now();

	for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
		try {
			if (attempt > 0) {
				if (config.verboseLogging) {
					console.log(`[withErrorHandling] Retry attempt ${attempt}/${config.maxRetries} for ${operationName}`);
				}
				await new Promise(resolve => setTimeout(resolve, config.retryDelay));
			}

			const result = await operation();
			
			if (attempt > 0) {
				// Recovery successful
				errorStats.recoveredErrors++;
				const recoveryTime = Date.now() - startTime;
				updateAverageRecoveryTime(recoveryTime);
				
				if (config.verboseLogging) {
					console.log(`[withErrorHandling] Recovery successful for ${operationName} after ${attempt} attempts`);
				}
			}

			return result;

		} catch (error) {
			const solverError = classifyError(error, operationName);
			lastError = solverError;
			
			// Update statistics
			errorStats.totalErrors++;
			const currentCount = errorStats.errorsByType.get(solverError.type) || 0;
			errorStats.errorsByType.set(solverError.type, currentCount + 1);

			if (config.verboseLogging) {
				console.error(`[withErrorHandling] ${operationName} failed (attempt ${attempt + 1}):`, solverError);
			}

			// Check if error is recoverable
			if (!solverError.recoverable) {
				errorStats.unrecoverableErrors++;
				throw solverError;
			}

			// Last attempt failed
			if (attempt === config.maxRetries) {
				errorStats.unrecoverableErrors++;
				throw solverError;
			}
		}
	}

	// Should never reach here, but TypeScript requires it
	throw lastError || new SolverError(SolverErrorType.UNKNOWN, 'Unknown error in withErrorHandling');
}

/**
 * Classify an error into a SolverError with appropriate type
 */
function classifyError(error: any, context: string): SolverError {
	let type = SolverErrorType.UNKNOWN;
	let recoverable = true;
	let message = error.message || 'Unknown error';

	// Browser-related errors
	if (message.includes('browser') || message.includes('page')) {
		if (message.includes('crash') || message.includes('disconnected')) {
			type = SolverErrorType.BROWSER_CRASH;
			recoverable = false;
		} else {
			type = SolverErrorType.BROWSER_CRASH;
		}
	}

	// Network-related errors
	if (message.includes('timeout') || message.includes('network') || message.includes('connection')) {
		type = SolverErrorType.NETWORK_TIMEOUT;
	}

	// Scraping-related errors
	if (context.includes('scrape') || message.includes('selector') || message.includes('element')) {
		type = SolverErrorType.SCRAPING_FAILED;
	}

	// Word play errors
	if (context.includes('play') || context.includes('click') || context.includes('submit')) {
		type = SolverErrorType.WORD_PLAY_FAILED;
	}

	// Undo errors
	if (context.includes('undo')) {
		type = SolverErrorType.UNDO_FAILED;
	}

	// Memory errors
	if (message.includes('memory') || message.includes('heap')) {
		type = SolverErrorType.MEMORY_EXHAUSTED;
		recoverable = false;
	}

	// Timeout errors
	if (message.includes('timeout') && context.includes('solve')) {
		type = SolverErrorType.TIMEOUT;
		recoverable = false;
	}

	return new SolverError(type, message, { originalError: error, context }, recoverable);
}

/**
 * Safe board scraping with error handling
 */
export async function safeScrapeBoard(
	page: Page,
	config: ErrorRecoveryConfig = DEFAULT_RECOVERY_CONFIG
): Promise<Board> {
	return withErrorHandling(
		async () => {
			try {
				return await scrapeBoard(page);
			} catch (error) {
				// Try fallback scraping strategies
				if (config.useFallbacks) {
					return await fallbackScrapeBoard(page);
				}
				throw error;
			}
		},
		'scrapeBoard',
		config
	);
}

/**
 * Safe word playing with error handling
 */
export async function safePlayWord(
	page: Page,
	positions: Array<[number, number]>,
	config: ErrorRecoveryConfig = DEFAULT_RECOVERY_CONFIG
): Promise<void> {
	return withErrorHandling(
		async () => {
			await playWord(page, positions);
		},
		'playWord',
		config
	);
}

/**
 * Safe undo with error handling
 */
export async function safeUndoLastMove(
	page: Page,
	config: ErrorRecoveryConfig = DEFAULT_RECOVERY_CONFIG
): Promise<void> {
	return withErrorHandling(
		async () => {
			const result = await undoLastMove(page);
			if (!result.success) {
				throw new SolverError(
					SolverErrorType.UNDO_FAILED,
					`Undo failed: ${result.message}`,
					{ result }
				);
			}
		},
		'undoLastMove',
		config
	);
}

/**
 * Fallback board scraping strategy
 */
async function fallbackScrapeBoard(page: Page): Promise<Board> {
	// Try refreshing the page and scraping again
	try {
		await page.reload();
		await page.waitForTimeout(3000);
		return await scrapeBoard(page);
	} catch (error) {
		// If all else fails, create a test board
		console.warn('[fallbackScrapeBoard] Using test board as fallback');
		const { createTestBoard } = await import('./scraping');
		return createTestBoard();
	}
}

/**
 * Validate board state for consistency
 */
export function validateBoardState(board: Board): void {
	const tiles = board.getAllTiles();
	
	if (tiles.length !== 25) {
		throw new SolverError(
			SolverErrorType.INVALID_BOARD_STATE,
			`Invalid board: expected 25 tiles, got ${tiles.length}`,
			{ tileCount: tiles.length }
		);
	}

	// Check for duplicate positions
	const positions = new Set<string>();
	for (const tile of tiles) {
		const key = `${tile.row},${tile.col}`;
		if (positions.has(key)) {
			throw new SolverError(
				SolverErrorType.INVALID_BOARD_STATE,
				`Duplicate tile position: ${key}`,
				{ position: key }
			);
		}
		positions.add(key);
	}

	// Check for invalid letters
	for (const tile of tiles) {
		if (!tile.letter || tile.letter.length !== 1 || !/[A-Z]/.test(tile.letter)) {
			throw new SolverError(
				SolverErrorType.INVALID_BOARD_STATE,
				`Invalid tile letter: "${tile.letter}" at [${tile.row}, ${tile.col}]`,
				{ tile: tile.toObject() }
			);
		}
	}
}

/**
 * Create a circuit breaker for repeated failures
 */
export class CircuitBreaker {
	private failures = 0;
	private lastFailureTime = 0;
	private state: 'closed' | 'open' | 'half-open' = 'closed';

	constructor(
		private readonly failureThreshold: number = 5,
		private readonly timeout: number = 60000 // 1 minute
	) {}

	async execute<T>(operation: () => Promise<T>): Promise<T> {
		if (this.state === 'open') {
			if (Date.now() - this.lastFailureTime > this.timeout) {
				this.state = 'half-open';
			} else {
				throw new SolverError(
					SolverErrorType.UNKNOWN,
					'Circuit breaker is open',
					{ state: this.state, failures: this.failures }
				);
			}
		}

		try {
			const result = await operation();
			this.onSuccess();
			return result;
		} catch (error) {
			this.onFailure();
			throw error;
		}
	}

	private onSuccess(): void {
		this.failures = 0;
		this.state = 'closed';
	}

	private onFailure(): void {
		this.failures++;
		this.lastFailureTime = Date.now();
		
		if (this.failures >= this.failureThreshold) {
			this.state = 'open';
		}
	}

	getState(): { state: string; failures: number; lastFailureTime: number } {
		return {
			state: this.state,
			failures: this.failures,
			lastFailureTime: this.lastFailureTime
		};
	}
}

/**
 * Update average recovery time
 */
function updateAverageRecoveryTime(newTime: number): void {
	if (errorStats.recoveredErrors === 1) {
		errorStats.averageRecoveryTime = newTime;
	} else {
		errorStats.averageRecoveryTime = 
			(errorStats.averageRecoveryTime * (errorStats.recoveredErrors - 1) + newTime) / 
			errorStats.recoveredErrors;
	}
}

/**
 * Get error statistics
 */
export function getErrorStats(): ErrorStats {
	return {
		...errorStats,
		errorsByType: new Map(errorStats.errorsByType)
	};
}

/**
 * Reset error statistics
 */
export function resetErrorStats(): void {
	errorStats.totalErrors = 0;
	errorStats.errorsByType.clear();
	errorStats.recoveredErrors = 0;
	errorStats.unrecoverableErrors = 0;
	errorStats.averageRecoveryTime = 0;
}

/**
 * Create a fallback result when solving fails completely
 */
export function createFallbackResult(reason: string): BestLineResult {
	return {
		total: 0,
		words: [],
		perRound: [],
		error: reason
	} as BestLineResult & { error: string };
}
