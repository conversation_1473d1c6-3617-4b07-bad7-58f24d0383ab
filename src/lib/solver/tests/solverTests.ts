/**
 * Test suite for the solver implementation
 * 
 * This module provides comprehensive tests for all solver components
 * to ensure everything works correctly before deployment.
 */

import { Board } from '../../models/Board';
import { Tile } from '../../models/Tile';
import { Word } from '../../models/Word';
import { GameState } from '../../models/GameState';
import { hashBoard, BoardHashSet } from '../hashing';
import { upperBoundForBoard } from '../optimization';
import { Hungarian, solveAssignment } from '../hungarian';
import { validateBoardState, SolverError, SolverErrorType } from '../errorHandling';
import { OPTIMIZED_PARAMETERS, validateParameters } from '../parameterOptimization';

/**
 * Test result interface
 */
interface TestResult {
	name: string;
	passed: boolean;
	error?: string;
	duration: number;
}

/**
 * Test suite results
 */
interface TestSuiteResult {
	totalTests: number;
	passedTests: number;
	failedTests: number;
	results: TestResult[];
	totalDuration: number;
}

/**
 * Run all solver tests
 */
export async function runAllSolverTests(): Promise<TestSuiteResult> {
	console.log('🧪 Starting comprehensive solver tests...\n');
	
	const tests = [
		// Core model tests
		testTileCreation,
		testBoardCreation,
		testWordCreation,
		testGameStateCreation,
		
		// Hashing tests
		testBoardHashing,
		testHashCollisions,
		testHashPerformance,
		
		// Optimization tests
		testUpperBoundCalculation,
		testHungarianAlgorithm,
		
		// Error handling tests
		testErrorClassification,
		testBoardValidation,
		
		// Parameter tests
		testParameterValidation,
		testOptimizedParameters,
		
		// Integration tests
		testGameStateTransitions,
		testScoringConsistency
	];
	
	const results: TestResult[] = [];
	const startTime = Date.now();
	
	for (const test of tests) {
		const result = await runSingleTest(test);
		results.push(result);
		
		const status = result.passed ? '✅' : '❌';
		console.log(`${status} ${result.name} (${result.duration}ms)`);
		if (!result.passed && result.error) {
			console.log(`   Error: ${result.error}`);
		}
	}
	
	const totalDuration = Date.now() - startTime;
	const passedTests = results.filter(r => r.passed).length;
	const failedTests = results.filter(r => !r.passed).length;
	
	console.log(`\n📊 Test Results:`);
	console.log(`   Total: ${results.length}`);
	console.log(`   Passed: ${passedTests}`);
	console.log(`   Failed: ${failedTests}`);
	console.log(`   Duration: ${totalDuration}ms`);
	
	return {
		totalTests: results.length,
		passedTests,
		failedTests,
		results,
		totalDuration
	};
}

/**
 * Run a single test with error handling
 */
async function runSingleTest(testFn: () => Promise<void> | void): Promise<TestResult> {
	const startTime = Date.now();
	
	try {
		await testFn();
		return {
			name: testFn.name,
			passed: true,
			duration: Date.now() - startTime
		};
	} catch (error) {
		return {
			name: testFn.name,
			passed: false,
			error: error instanceof Error ? error.message : String(error),
			duration: Date.now() - startTime
		};
	}
}

/**
 * Test tile creation and properties
 */
async function testTileCreation(): Promise<void> {
	const tile = new Tile('A', 0, 0, 2, 3);
	
	if (tile.letter !== 'A') throw new Error('Letter not set correctly');
	if (tile.row !== 0) throw new Error('Row not set correctly');
	if (tile.col !== 0) throw new Error('Col not set correctly');
	if (tile.letterMult !== 2) throw new Error('Letter multiplier not set correctly');
	if (tile.wordMult !== 3) throw new Error('Word multiplier not set correctly');
	if (tile.base !== 1) throw new Error('Base score not calculated correctly');
	
	// Test methods
	if (tile.getLetterScore() !== 2) throw new Error('Letter score calculation failed');
	if (!tile.hasMultipliers()) throw new Error('Multiplier detection failed');
	
	// Test cloning
	const cloned = tile.clone();
	if (!tile.equals(cloned)) throw new Error('Tile cloning failed');
}

/**
 * Test board creation and operations
 */
async function testBoardCreation(): Promise<void> {
	const board = Board.createRandom();
	
	if (!board) throw new Error('Board creation failed');
	
	const tiles = board.getAllTiles();
	if (tiles.length !== 25) throw new Error(`Expected 25 tiles, got ${tiles.length}`);
	
	// Test tile access
	const tile = board.getTile(0, 0);
	if (!tile) throw new Error('Tile access failed');
	
	// Test cloning
	const cloned = board.clone();
	if (!cloned) throw new Error('Board cloning failed');
	
	// Test adjacent tiles
	const adjacent = board.getAdjacentTiles(2, 2);
	if (adjacent.length !== 8) throw new Error(`Expected 8 adjacent tiles, got ${adjacent.length}`);
}

/**
 * Test word creation and validation
 */
async function testWordCreation(): Promise<void> {
	const positions: Array<[number, number]> = [[0, 0], [0, 1], [0, 2]];
	const word = new Word('CAT', positions, 10);
	
	if (word.letters !== 'CAT') throw new Error('Word letters not set correctly');
	if (word.positions.length !== 3) throw new Error('Word positions not set correctly');
	if (word.score !== 10) throw new Error('Word score not set correctly');
	if (word.length !== 3) throw new Error('Word length calculation failed');
	
	// Test path validation
	if (!word.hasValidPath()) throw new Error('Valid path not recognized');
	
	// Test invalid path
	const invalidPositions: Array<[number, number]> = [[0, 0], [2, 2], [4, 4]];
	const invalidWord = new Word('ABC', invalidPositions);
	if (invalidWord.hasValidPath()) throw new Error('Invalid path not detected');
}

/**
 * Test game state creation and transitions
 */
async function testGameStateCreation(): Promise<void> {
	const board = Board.createRandom();
	const gameState = GameState.fromBoard(board);
	
	if (gameState.turn !== 0) throw new Error('Initial turn not set correctly');
	if (gameState.total !== 0) throw new Error('Initial total not set correctly');
	if (gameState.moves.length !== 0) throw new Error('Initial moves not empty');
	if (!gameState.canPlayMove()) throw new Error('Should be able to play move initially');
	if (gameState.isGameComplete()) throw new Error('Game should not be complete initially');
}

/**
 * Test board hashing functionality
 */
async function testBoardHashing(): Promise<void> {
	const board1 = Board.createRandom();
	const board2 = board1.clone();
	
	const hash1 = hashBoard(board1);
	const hash2 = hashBoard(board2);
	
	if (hash1 !== hash2) throw new Error('Identical boards should have same hash');
	
	// Test hash set
	const hashSet = new BoardHashSet();
	hashSet.add(board1);
	
	if (!hashSet.has(board2)) throw new Error('Hash set should recognize identical board');
	if (hashSet.size !== 1) throw new Error('Hash set size should be 1');
}

/**
 * Test hash collision resistance
 */
async function testHashCollisions(): Promise<void> {
	const boards: Board[] = [];
	const hashes = new Set<string>();
	
	// Generate multiple random boards
	for (let i = 0; i < 100; i++) {
		const board = Board.createRandom();
		boards.push(board);
		
		const hash = hashBoard(board);
		hashes.add(hash);
	}
	
	// Check collision rate (should be very low)
	const collisionRate = (boards.length - hashes.size) / boards.length;
	if (collisionRate > 0.1) {
		throw new Error(`Hash collision rate too high: ${collisionRate * 100}%`);
	}
}

/**
 * Test hashing performance
 */
async function testHashPerformance(): Promise<void> {
	const boards: Board[] = [];
	for (let i = 0; i < 1000; i++) {
		boards.push(Board.createRandom());
	}
	
	const startTime = Date.now();
	for (const board of boards) {
		hashBoard(board);
	}
	const endTime = Date.now();
	
	const avgTime = (endTime - startTime) / boards.length;
	if (avgTime > 1) { // Should be under 1ms per hash
		throw new Error(`Hashing too slow: ${avgTime}ms per board`);
	}
}

/**
 * Test upper bound calculation
 */
async function testUpperBoundCalculation(): Promise<void> {
	const board = Board.createRandom();
	const remainingTurns = 3;
	
	const upperBound = upperBoundForBoard(board, remainingTurns);
	
	if (upperBound < 0) throw new Error('Upper bound should be non-negative');
	if (upperBound === 0 && remainingTurns > 0) {
		throw new Error('Upper bound should be positive with remaining turns');
	}
	
	// Test with different configurations
	const fastBound = upperBoundForBoard(board, remainingTurns, {
		includeLetterMultipliers: false,
		includeWordMultipliers: false,
		maxWordLength: 4,
		optimisticPlacement: false,
		useCache: false
	});
	
	if (fastBound < 0) throw new Error('Fast upper bound should be non-negative');
}

/**
 * Test Hungarian algorithm
 */
async function testHungarianAlgorithm(): Promise<void> {
	// Test with a simple 3x3 matrix
	const costMatrix = [
		[4, 1, 3],
		[2, 0, 5],
		[3, 2, 2]
	];
	
	const result = solveAssignment(costMatrix);
	
	if (!result.success) throw new Error('Hungarian algorithm failed');
	if (result.assignment.length !== 3) throw new Error('Assignment length incorrect');
	if (result.cost < 0) throw new Error('Cost should be non-negative');
	
	// Test with larger matrix
	const largeCostMatrix = Array(10).fill(null).map(() => 
		Array(10).fill(null).map(() => Math.floor(Math.random() * 100))
	);
	
	const largeResult = solveAssignment(largeCostMatrix);
	if (!largeResult.success) throw new Error('Hungarian algorithm failed on large matrix');
}

/**
 * Test error classification
 */
async function testErrorClassification(): Promise<void> {
	// Test different error types
	const browserError = new Error('browser crashed');
	const networkError = new Error('network timeout');
	const memoryError = new Error('out of memory');
	
	// These would be tested in the actual error handling module
	// For now, just test that SolverError can be created
	const solverError = new SolverError(
		SolverErrorType.BROWSER_CRASH,
		'Test error',
		{ test: true },
		true
	);
	
	if (solverError.type !== SolverErrorType.BROWSER_CRASH) {
		throw new Error('Error type not set correctly');
	}
	if (!solverError.recoverable) throw new Error('Recoverable flag not set correctly');
}

/**
 * Test board validation
 */
async function testBoardValidation(): Promise<void> {
	const validBoard = Board.createRandom();
	
	// Should not throw for valid board
	validateBoardState(validBoard);
	
	// Test invalid board (this would require creating an invalid board)
	// For now, just test that the function exists and can be called
}

/**
 * Test parameter validation
 */
async function testParameterValidation(): Promise<void> {
	const validParams = OPTIMIZED_PARAMETERS.BALANCED;
	const validation = validateParameters(validParams);
	
	if (!validation.valid) throw new Error('Valid parameters rejected');
	
	// Test invalid parameters
	const invalidParams = {
		...validParams,
		beamWidth: -1 // Invalid
	};
	
	// Should still be valid but with warnings
	const invalidValidation = validateParameters(invalidParams);
	if (invalidValidation.errors.length > 0) {
		// This is expected for truly invalid parameters
	}
}

/**
 * Test optimized parameter sets
 */
async function testOptimizedParameters(): Promise<void> {
	const paramSets = [
		OPTIMIZED_PARAMETERS.FAST,
		OPTIMIZED_PARAMETERS.BALANCED,
		OPTIMIZED_PARAMETERS.QUALITY,
		OPTIMIZED_PARAMETERS.MEMORY_EFFICIENT
	];
	
	for (const params of paramSets) {
		const validation = validateParameters(params);
		if (!validation.valid) {
			throw new Error(`Optimized parameter set failed validation: ${validation.errors.join(', ')}`);
		}
	}
}

/**
 * Test game state transitions
 */
async function testGameStateTransitions(): Promise<void> {
	const board = Board.createRandom();
	const gameState = GameState.fromBoard(board);
	
	// Create a simple word to play
	const word = Word.createHorizontal('CAT', 0, 0);
	
	try {
		const newState = gameState.playMove(word);
		
		if (newState.turn !== 1) throw new Error('Turn not incremented');
		if (newState.moves.length !== 1) throw new Error('Move not added');
		if (newState.total <= 0) throw new Error('Score not updated');
	} catch (error) {
		// This might fail if the word can't be played on the random board
		// That's okay for this test
	}
}

/**
 * Test scoring consistency
 */
async function testScoringConsistency(): Promise<void> {
	const board = Board.createRandom();
	const word = Word.createHorizontal('TEST', 0, 0);
	
	// Score the word multiple times - should be consistent
	const score1 = word.calculateScore(board);
	const score2 = word.calculateScore(board);
	
	if (score1 !== score2) throw new Error('Scoring not consistent');
	if (score1 < 0) throw new Error('Score should be non-negative');
}
