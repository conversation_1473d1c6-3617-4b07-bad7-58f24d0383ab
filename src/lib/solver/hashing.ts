/**
 * Board hashing utilities for efficient state deduplication
 * 
 * This module provides fast hashing functions for board states to enable
 * efficient duplicate detection in the search algorithm.
 */

import type { Board } from '../models/Board';
import type { GameState } from '../models/GameState';
import { GAME_CONFIG } from '../types';

/**
 * Hash configuration options
 */
interface HashConfig {
	/** Include multipliers in hash (slower but more accurate) */
	includeMultipliers: boolean;
	/** Use position-sensitive hashing */
	positionSensitive: boolean;
	/** Hash algorithm to use */
	algorithm: 'fnv1a' | 'djb2' | 'simple';
}

/**
 * Default hash configuration
 */
const DEFAULT_HASH_CONFIG: HashConfig = {
	includeMultipliers: true,
	positionSensitive: true,
	algorithm: 'fnv1a'
};

/**
 * Generate a hash for a board state
 */
export function hashBoard(board: Board, config: HashConfig = DEFAULT_HASH_CONFIG): string {
	switch (config.algorithm) {
		case 'fnv1a':
			return hashBoardFNV1a(board, config);
		case 'djb2':
			return hashBoardDJB2(board, config);
		case 'simple':
			return hashBoardSimple(board, config);
		default:
			return hashBoardFNV1a(board, config);
	}
}

/**
 * Generate a hash for a complete game state
 */
export function hashGameState(gameState: GameState, config: HashConfig = DEFAULT_HASH_CONFIG): string {
	const boardHash = hashBoard(gameState.board, config);
	const turnHash = gameState.turn.toString();
	const totalHash = gameState.total.toString();
	
	// Combine hashes
	return hashString(`${boardHash}:${turnHash}:${totalHash}`, config.algorithm);
}

/**
 * Fast FNV-1a hash implementation
 */
function hashBoardFNV1a(board: Board, config: HashConfig): string {
	let hash = 2166136261; // FNV offset basis
	const fnvPrime = 16777619;
	
	for (let row = 0; row < GAME_CONFIG.BOARD_SIZE; row++) {
		for (let col = 0; col < GAME_CONFIG.BOARD_SIZE; col++) {
			const tile = board.getTile(row, col);
			if (!tile) continue;
			
			// Hash the letter
			const letterCode = tile.letter.charCodeAt(0);
			hash ^= letterCode;
			hash = (hash * fnvPrime) >>> 0; // Unsigned 32-bit
			
			if (config.positionSensitive) {
				// Include position in hash
				hash ^= (row * 5 + col);
				hash = (hash * fnvPrime) >>> 0;
			}
			
			if (config.includeMultipliers) {
				// Include multipliers
				hash ^= (tile.letterMult << 4) | tile.wordMult;
				hash = (hash * fnvPrime) >>> 0;
			}
		}
	}
	
	return hash.toString(36); // Base 36 for shorter strings
}

/**
 * DJB2 hash implementation (alternative algorithm)
 */
function hashBoardDJB2(board: Board, config: HashConfig): string {
	let hash = 5381;
	
	for (let row = 0; row < GAME_CONFIG.BOARD_SIZE; row++) {
		for (let col = 0; col < GAME_CONFIG.BOARD_SIZE; col++) {
			const tile = board.getTile(row, col);
			if (!tile) continue;
			
			// Hash the letter
			const letterCode = tile.letter.charCodeAt(0);
			hash = ((hash << 5) + hash + letterCode) >>> 0;
			
			if (config.positionSensitive) {
				hash = ((hash << 5) + hash + (row * 5 + col)) >>> 0;
			}
			
			if (config.includeMultipliers) {
				hash = ((hash << 5) + hash + (tile.letterMult << 4) + tile.wordMult) >>> 0;
			}
		}
	}
	
	return hash.toString(36);
}

/**
 * Simple string-based hash (readable but slower)
 */
function hashBoardSimple(board: Board, config: HashConfig): string {
	const parts: string[] = [];
	
	for (let row = 0; row < GAME_CONFIG.BOARD_SIZE; row++) {
		for (let col = 0; col < GAME_CONFIG.BOARD_SIZE; col++) {
			const tile = board.getTile(row, col);
			if (!tile) {
				parts.push('_');
				continue;
			}
			
			let tileStr = tile.letter;
			
			if (config.includeMultipliers) {
				if (tile.letterMult > 1) tileStr += `L${tile.letterMult}`;
				if (tile.wordMult > 1) tileStr += `W${tile.wordMult}`;
			}
			
			if (config.positionSensitive) {
				tileStr += `@${row},${col}`;
			}
			
			parts.push(tileStr);
		}
	}
	
	return parts.join('|');
}

/**
 * Hash a string using specified algorithm
 */
function hashString(str: string, algorithm: HashConfig['algorithm']): string {
	switch (algorithm) {
		case 'fnv1a':
			return hashStringFNV1a(str);
		case 'djb2':
			return hashStringDJB2(str);
		case 'simple':
			return str; // Return as-is for simple algorithm
		default:
			return hashStringFNV1a(str);
	}
}

/**
 * FNV-1a string hash
 */
function hashStringFNV1a(str: string): string {
	let hash = 2166136261;
	const fnvPrime = 16777619;
	
	for (let i = 0; i < str.length; i++) {
		hash ^= str.charCodeAt(i);
		hash = (hash * fnvPrime) >>> 0;
	}
	
	return hash.toString(36);
}

/**
 * DJB2 string hash
 */
function hashStringDJB2(str: string): string {
	let hash = 5381;
	
	for (let i = 0; i < str.length; i++) {
		hash = ((hash << 5) + hash + str.charCodeAt(i)) >>> 0;
	}
	
	return hash.toString(36);
}

/**
 * Create a hash map for efficient board state storage
 */
export class BoardHashMap<T> {
	private map = new Map<string, T>();
	private config: HashConfig;
	
	constructor(config: HashConfig = DEFAULT_HASH_CONFIG) {
		this.config = config;
	}
	
	/**
	 * Set a value for a board state
	 */
	set(board: Board, value: T): void {
		const hash = hashBoard(board, this.config);
		this.map.set(hash, value);
	}
	
	/**
	 * Get a value for a board state
	 */
	get(board: Board): T | undefined {
		const hash = hashBoard(board, this.config);
		return this.map.get(hash);
	}
	
	/**
	 * Check if a board state exists
	 */
	has(board: Board): boolean {
		const hash = hashBoard(board, this.config);
		return this.map.has(hash);
	}
	
	/**
	 * Delete a board state
	 */
	delete(board: Board): boolean {
		const hash = hashBoard(board, this.config);
		return this.map.delete(hash);
	}
	
	/**
	 * Clear all entries
	 */
	clear(): void {
		this.map.clear();
	}
	
	/**
	 * Get the number of stored states
	 */
	get size(): number {
		return this.map.size;
	}
	
	/**
	 * Get all stored hashes
	 */
	getHashes(): string[] {
		return Array.from(this.map.keys());
	}
	
	/**
	 * Get all stored values
	 */
	getValues(): T[] {
		return Array.from(this.map.values());
	}
}

/**
 * Create a hash set for efficient board state deduplication
 */
export class BoardHashSet {
	private set = new Set<string>();
	private config: HashConfig;
	
	constructor(config: HashConfig = DEFAULT_HASH_CONFIG) {
		this.config = config;
	}
	
	/**
	 * Add a board state to the set
	 */
	add(board: Board): void {
		const hash = hashBoard(board, this.config);
		this.set.add(hash);
	}
	
	/**
	 * Check if a board state exists in the set
	 */
	has(board: Board): boolean {
		const hash = hashBoard(board, this.config);
		return this.set.has(hash);
	}
	
	/**
	 * Delete a board state from the set
	 */
	delete(board: Board): boolean {
		const hash = hashBoard(board, this.config);
		return this.set.delete(hash);
	}
	
	/**
	 * Clear all entries
	 */
	clear(): void {
		this.set.clear();
	}
	
	/**
	 * Get the number of stored states
	 */
	get size(): number {
		return this.set.size;
	}
	
	/**
	 * Get all stored hashes
	 */
	getHashes(): string[] {
		return Array.from(this.set);
	}
}

/**
 * Benchmark different hashing algorithms
 */
export function benchmarkHashingAlgorithms(boards: Board[], iterations = 1000): {
	fnv1a: { time: number; collisions: number };
	djb2: { time: number; collisions: number };
	simple: { time: number; collisions: number };
} {
	const algorithms: HashConfig['algorithm'][] = ['fnv1a', 'djb2', 'simple'];
	const results: any = {};
	
	for (const algorithm of algorithms) {
		const config: HashConfig = { ...DEFAULT_HASH_CONFIG, algorithm };
		const hashes = new Set<string>();
		
		const startTime = performance.now();
		
		for (let i = 0; i < iterations; i++) {
			for (const board of boards) {
				const hash = hashBoard(board, config);
				hashes.add(hash);
			}
		}
		
		const endTime = performance.now();
		const time = endTime - startTime;
		const collisions = (boards.length * iterations) - hashes.size;
		
		results[algorithm] = { time, collisions };
	}
	
	return results;
}

/**
 * Analyze hash distribution quality
 */
export function analyzeHashDistribution(boards: Board[], config: HashConfig = DEFAULT_HASH_CONFIG): {
	uniqueHashes: number;
	collisionRate: number;
	averageHashLength: number;
	hashLengthVariance: number;
} {
	const hashes = boards.map(board => hashBoard(board, config));
	const uniqueHashes = new Set(hashes).size;
	const collisionRate = (boards.length - uniqueHashes) / boards.length;
	
	const hashLengths = hashes.map(hash => hash.length);
	const averageHashLength = hashLengths.reduce((sum, len) => sum + len, 0) / hashLengths.length;
	
	const variance = hashLengths.reduce((sum, len) => {
		const diff = len - averageHashLength;
		return sum + diff * diff;
	}, 0) / hashLengths.length;
	
	return {
		uniqueHashes,
		collisionRate,
		averageHashLength,
		hashLengthVariance: variance
	};
}
