/**
 * Hungarian Algorithm implementation for optimal assignment problems
 * 
 * This module implements the Hungarian algorithm (also known as the <PERSON><PERSON><PERSON> algorithm)
 * for solving the assignment problem in polynomial time. Used for calculating
 * tight upper bounds in the solver.
 */

/**
 * Result of the Hungarian algorithm
 */
export interface HungarianResult {
	/** Total cost of the optimal assignment */
	cost: number;
	/** Assignment array where assignment[i] = j means row i is assigned to column j */
	assignment: number[];
	/** Whether the algorithm found a valid solution */
	success: boolean;
}

/**
 * Hungarian algorithm implementation for minimum cost assignment
 */
export class Hungarian {
	private matrix: number[][];
	private rows: number;
	private cols: number;
	private rowCover: boolean[];
	private colCover: boolean[];
	private zeros: Array<[number, number]>;
	private starredZeros: boolean[][];
	private primedZeros: boolean[][];

	constructor(costMatrix: number[][]) {
		if (costMatrix.length === 0 || costMatrix[0].length === 0) {
			throw new Error('Cost matrix cannot be empty');
		}

		this.rows = costMatrix.length;
		this.cols = costMatrix[0].length;

		// Ensure matrix is square by padding with zeros if necessary
		const size = Math.max(this.rows, this.cols);
		this.matrix = Array(size).fill(null).map(() => Array(size).fill(0));

		// Copy input matrix
		for (let i = 0; i < this.rows; i++) {
			for (let j = 0; j < this.cols; j++) {
				this.matrix[i][j] = costMatrix[i][j];
			}
		}

		// Initialize working arrays
		this.rowCover = Array(size).fill(false);
		this.colCover = Array(size).fill(false);
		this.starredZeros = Array(size).fill(null).map(() => Array(size).fill(false));
		this.primedZeros = Array(size).fill(null).map(() => Array(size).fill(false));
		this.zeros = [];
	}

	/**
	 * Solve the assignment problem
	 */
	solve(): HungarianResult {
		try {
			// Step 1: Subtract row minima
			this.subtractRowMinima();

			// Step 2: Subtract column minima
			this.subtractColumnMinima();

			// Step 3: Cover all zeros with minimum number of lines
			this.coverZeros();

			// Step 4: Main algorithm loop
			while (!this.isDone()) {
				this.step();
			}

			// Extract solution
			const assignment = this.extractAssignment();
			const cost = this.calculateCost(assignment);

			return {
				cost,
				assignment,
				success: true
			};
		} catch (error) {
			return {
				cost: Infinity,
				assignment: [],
				success: false
			};
		}
	}

	/**
	 * Step 1: Subtract the minimum value in each row from all elements in that row
	 */
	private subtractRowMinima(): void {
		for (let i = 0; i < this.matrix.length; i++) {
			const rowMin = Math.min(...this.matrix[i]);
			for (let j = 0; j < this.matrix[i].length; j++) {
				this.matrix[i][j] -= rowMin;
			}
		}
	}

	/**
	 * Step 2: Subtract the minimum value in each column from all elements in that column
	 */
	private subtractColumnMinima(): void {
		for (let j = 0; j < this.matrix[0].length; j++) {
			let colMin = Infinity;
			for (let i = 0; i < this.matrix.length; i++) {
				colMin = Math.min(colMin, this.matrix[i][j]);
			}
			for (let i = 0; i < this.matrix.length; i++) {
				this.matrix[i][j] -= colMin;
			}
		}
	}

	/**
	 * Step 3: Cover all zeros with the minimum number of lines
	 */
	private coverZeros(): void {
		// Find all zeros and star them if possible
		for (let i = 0; i < this.matrix.length; i++) {
			for (let j = 0; j < this.matrix[i].length; j++) {
				if (this.matrix[i][j] === 0 && !this.rowCover[i] && !this.colCover[j]) {
					this.starredZeros[i][j] = true;
					this.rowCover[i] = true;
					this.colCover[j] = true;
				}
			}
		}

		// Clear covers for next step
		this.clearCovers();

		// Cover columns containing starred zeros
		for (let i = 0; i < this.matrix.length; i++) {
			for (let j = 0; j < this.matrix[i].length; j++) {
				if (this.starredZeros[i][j]) {
					this.colCover[j] = true;
				}
			}
		}
	}

	/**
	 * Main algorithm step
	 */
	private step(): void {
		// Find an uncovered zero
		const uncoveredZero = this.findUncoveredZero();

		if (uncoveredZero) {
			const [row, col] = uncoveredZero;

			// Prime the zero
			this.primedZeros[row][col] = true;

			// Check if there's a starred zero in the same row
			const starredCol = this.findStarredZeroInRow(row);

			if (starredCol !== -1) {
				// Cover the row and uncover the column
				this.rowCover[row] = true;
				this.colCover[starredCol] = false;
			} else {
				// Construct augmenting path
				this.constructAugmentingPath(row, col);
				this.clearCovers();
				this.clearPrimes();

				// Cover columns with starred zeros
				for (let i = 0; i < this.matrix.length; i++) {
					for (let j = 0; j < this.matrix[i].length; j++) {
						if (this.starredZeros[i][j]) {
							this.colCover[j] = true;
						}
					}
				}
			}
		} else {
			// No uncovered zeros, need to create more
			this.createMoreZeros();
		}
	}

	/**
	 * Find an uncovered zero in the matrix
	 */
	private findUncoveredZero(): [number, number] | null {
		for (let i = 0; i < this.matrix.length; i++) {
			for (let j = 0; j < this.matrix[i].length; j++) {
				if (this.matrix[i][j] === 0 && !this.rowCover[i] && !this.colCover[j]) {
					return [i, j];
				}
			}
		}
		return null;
	}

	/**
	 * Find starred zero in the given row
	 */
	private findStarredZeroInRow(row: number): number {
		for (let j = 0; j < this.matrix[row].length; j++) {
			if (this.starredZeros[row][j]) {
				return j;
			}
		}
		return -1;
	}

	/**
	 * Find primed zero in the given column
	 */
	private findPrimedZeroInColumn(col: number): number {
		for (let i = 0; i < this.matrix.length; i++) {
			if (this.primedZeros[i][col]) {
				return i;
			}
		}
		return -1;
	}

	/**
	 * Construct augmenting path and update starred zeros
	 */
	private constructAugmentingPath(startRow: number, startCol: number): void {
		const path: Array<[number, number]> = [[startRow, startCol]];
		let currentCol = startCol;

		while (true) {
			// Find starred zero in current column
			const starredRow = this.findStarredZeroInColumn(currentCol);
			if (starredRow === -1) break;

			path.push([starredRow, currentCol]);

			// Find primed zero in starred row
			const primedCol = this.findPrimedZeroInRow(starredRow);
			if (primedCol === -1) break;

			path.push([starredRow, primedCol]);
			currentCol = primedCol;
		}

		// Update starred zeros along the path
		for (let i = 0; i < path.length; i++) {
			const [row, col] = path[i];
			if (i % 2 === 0) {
				// Star the zero
				this.starredZeros[row][col] = true;
			} else {
				// Unstar the zero
				this.starredZeros[row][col] = false;
			}
		}
	}

	/**
	 * Find starred zero in the given column
	 */
	private findStarredZeroInColumn(col: number): number {
		for (let i = 0; i < this.matrix.length; i++) {
			if (this.starredZeros[i][col]) {
				return i;
			}
		}
		return -1;
	}

	/**
	 * Find primed zero in the given row
	 */
	private findPrimedZeroInRow(row: number): number {
		for (let j = 0; j < this.matrix[row].length; j++) {
			if (this.primedZeros[row][j]) {
				return j;
			}
		}
		return -1;
	}

	/**
	 * Create more zeros by subtracting the minimum uncovered value
	 */
	private createMoreZeros(): void {
		// Find minimum uncovered value
		let minUncovered = Infinity;
		for (let i = 0; i < this.matrix.length; i++) {
			for (let j = 0; j < this.matrix[i].length; j++) {
				if (!this.rowCover[i] && !this.colCover[j]) {
					minUncovered = Math.min(minUncovered, this.matrix[i][j]);
				}
			}
		}

		// Subtract from uncovered elements, add to doubly covered elements
		for (let i = 0; i < this.matrix.length; i++) {
			for (let j = 0; j < this.matrix[i].length; j++) {
				if (this.rowCover[i] && this.colCover[j]) {
					// Doubly covered
					this.matrix[i][j] += minUncovered;
				} else if (!this.rowCover[i] && !this.colCover[j]) {
					// Uncovered
					this.matrix[i][j] -= minUncovered;
				}
			}
		}
	}

	/**
	 * Check if the algorithm is done
	 */
	private isDone(): boolean {
		let coveredCols = 0;
		for (let j = 0; j < this.colCover.length; j++) {
			if (this.colCover[j]) {
				coveredCols++;
			}
		}
		return coveredCols >= Math.min(this.rows, this.cols);
	}

	/**
	 * Clear all covers
	 */
	private clearCovers(): void {
		this.rowCover.fill(false);
		this.colCover.fill(false);
	}

	/**
	 * Clear all primed zeros
	 */
	private clearPrimes(): void {
		for (let i = 0; i < this.primedZeros.length; i++) {
			this.primedZeros[i].fill(false);
		}
	}

	/**
	 * Extract the final assignment
	 */
	private extractAssignment(): number[] {
		const assignment: number[] = Array(this.rows).fill(-1);

		for (let i = 0; i < Math.min(this.rows, this.starredZeros.length); i++) {
			for (let j = 0; j < Math.min(this.cols, this.starredZeros[i].length); j++) {
				if (this.starredZeros[i][j]) {
					assignment[i] = j;
					break;
				}
			}
		}

		return assignment;
	}

	/**
	 * Calculate the total cost of an assignment
	 */
	private calculateCost(assignment: number[]): number {
		let totalCost = 0;
		for (let i = 0; i < assignment.length; i++) {
			if (assignment[i] !== -1 && assignment[i] < this.cols) {
				totalCost += this.matrix[i][assignment[i]];
			}
		}
		return totalCost;
	}
}

/**
 * Solve assignment problem using Hungarian algorithm
 */
export function solveAssignment(costMatrix: number[][]): HungarianResult {
	const hungarian = new Hungarian(costMatrix);
	return hungarian.solve();
}

/**
 * Create a cost matrix for tile-to-word assignment
 * Used for calculating tight upper bounds
 */
export function createTileWordCostMatrix(
	tiles: Array<{ letter: string; value: number }>,
	words: Array<{ letters: string; score: number }>
): number[][] {
	const matrix: number[][] = [];

	for (let i = 0; i < tiles.length; i++) {
		matrix[i] = [];
		for (let j = 0; j < words.length; j++) {
			// Cost is negative score (since Hungarian finds minimum cost)
			// Higher scores = lower costs
			const word = words[j];
			const tile = tiles[i];

			// Check if tile can contribute to this word
			if (word.letters.includes(tile.letter)) {
				matrix[i][j] = -word.score; // Negative for maximization
			} else {
				matrix[i][j] = 0; // No contribution
			}
		}
	}

	return matrix;
}

/**
 * Benchmark Hungarian algorithm performance
 */
export function benchmarkHungarian(sizes: number[]): Array<{
	size: number;
	time: number;
	cost: number;
}> {
	const results: Array<{ size: number; time: number; cost: number }> = [];

	for (const size of sizes) {
		// Generate random cost matrix
		const matrix: number[][] = [];
		for (let i = 0; i < size; i++) {
			matrix[i] = [];
			for (let j = 0; j < size; j++) {
				matrix[i][j] = Math.floor(Math.random() * 100);
			}
		}

		const startTime = performance.now();
		const result = solveAssignment(matrix);
		const endTime = performance.now();

		results.push({
			size,
			time: endTime - startTime,
			cost: result.cost
		});
	}

	return results;
}
