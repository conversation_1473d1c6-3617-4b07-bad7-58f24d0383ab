/**
 * Parameter optimization for the beam search solver
 * 
 * This module provides utilities for fine-tuning search parameters
 * to achieve optimal performance across different board configurations.
 */

import type { Page } from '@playwright/test';
import { solveDailyBoard } from './solveDailyBoard';
import type { BestLineResult } from '../types';

/**
 * Parameter set for optimization
 */
export interface ParameterSet {
	beamWidth: number;
	maxDepth: number;
	timeLimit: number;
	usePruning: boolean;
	useDeduplication: boolean;
	maxWordsPerPosition: number;
}

/**
 * Optimization result for a parameter set
 */
interface OptimizationResult {
	parameters: ParameterSet;
	averageScore: number;
	averageTime: number;
	successRate: number;
	efficiency: number; // Score per second
	results: BestLineResult[];
}

/**
 * Default parameter ranges for optimization
 */
const PARAMETER_RANGES = {
	beamWidth: [20, 40, 60, 80, 100, 120],
	maxDepth: [5], // Fixed at game maximum
	timeLimit: [60000, 120000, 180000, 300000], // 1-5 minutes
	usePruning: [true, false],
	useDeduplication: [true, false],
	maxWordsPerPosition: [25, 50, 75, 100, 150]
};

/**
 * Optimized parameter sets for different scenarios
 */
export const OPTIMIZED_PARAMETERS = {
	// Fast solving (under 1 minute)
	FAST: {
		beamWidth: 40,
		maxDepth: 5,
		timeLimit: 60000,
		usePruning: true,
		useDeduplication: true,
		maxWordsPerPosition: 50
	},
	
	// Balanced performance and quality
	BALANCED: {
		beamWidth: 60,
		maxDepth: 5,
		timeLimit: 180000,
		usePruning: true,
		useDeduplication: true,
		maxWordsPerPosition: 75
	},
	
	// High quality (longer solving time)
	QUALITY: {
		beamWidth: 100,
		maxDepth: 5,
		timeLimit: 300000,
		usePruning: true,
		useDeduplication: true,
		maxWordsPerPosition: 100
	},
	
	// Memory efficient
	MEMORY_EFFICIENT: {
		beamWidth: 30,
		maxDepth: 5,
		timeLimit: 120000,
		usePruning: true,
		useDeduplication: false,
		maxWordsPerPosition: 40
	}
} as const;

/**
 * Optimize parameters using grid search
 */
export async function optimizeParameters(
	page: Page,
	testIterations: number = 3,
	parameterRanges: Partial<typeof PARAMETER_RANGES> = PARAMETER_RANGES
): Promise<OptimizationResult[]> {
	const results: OptimizationResult[] = [];
	
	// Generate all parameter combinations
	const combinations = generateParameterCombinations(parameterRanges);
	
	console.log(`[optimizeParameters] Testing ${combinations.length} parameter combinations...`);
	
	for (let i = 0; i < combinations.length; i++) {
		const params = combinations[i];
		console.log(`[optimizeParameters] Testing combination ${i + 1}/${combinations.length}:`, params);
		
		try {
			const result = await evaluateParameterSet(page, params, testIterations);
			results.push(result);
			
			console.log(`[optimizeParameters] Result: Score=${result.averageScore.toFixed(1)}, Time=${result.averageTime.toFixed(0)}ms, Efficiency=${result.efficiency.toFixed(3)}`);
		} catch (error) {
			console.error(`[optimizeParameters] Error testing parameters:`, error);
		}
	}
	
	// Sort by efficiency (score per second)
	results.sort((a, b) => b.efficiency - a.efficiency);
	
	return results;
}

/**
 * Generate all combinations of parameters
 */
function generateParameterCombinations(ranges: Partial<typeof PARAMETER_RANGES>): ParameterSet[] {
	const combinations: ParameterSet[] = [];
	
	const beamWidths = ranges.beamWidth || PARAMETER_RANGES.beamWidth;
	const maxDepths = ranges.maxDepth || PARAMETER_RANGES.maxDepth;
	const timeLimits = ranges.timeLimit || PARAMETER_RANGES.timeLimit;
	const pruningOptions = ranges.usePruning || PARAMETER_RANGES.usePruning;
	const deduplicationOptions = ranges.useDeduplication || PARAMETER_RANGES.useDeduplication;
	const maxWordsOptions = ranges.maxWordsPerPosition || PARAMETER_RANGES.maxWordsPerPosition;
	
	for (const beamWidth of beamWidths) {
		for (const maxDepth of maxDepths) {
			for (const timeLimit of timeLimits) {
				for (const usePruning of pruningOptions) {
					for (const useDeduplication of deduplicationOptions) {
						for (const maxWordsPerPosition of maxWordsOptions) {
							combinations.push({
								beamWidth,
								maxDepth,
								timeLimit,
								usePruning,
								useDeduplication,
								maxWordsPerPosition
							});
						}
					}
				}
			}
		}
	}
	
	return combinations;
}

/**
 * Evaluate a specific parameter set
 */
async function evaluateParameterSet(
	page: Page,
	parameters: ParameterSet,
	iterations: number
): Promise<OptimizationResult> {
	const results: BestLineResult[] = [];
	const times: number[] = [];
	let successCount = 0;
	
	for (let i = 0; i < iterations; i++) {
		try {
			const startTime = Date.now();
			
			const result = await solveDailyBoard(page, {
				...parameters,
				debug: false
			});
			
			const endTime = Date.now();
			const elapsed = endTime - startTime;
			
			results.push(result);
			times.push(elapsed);
			successCount++;
			
		} catch (error) {
			console.warn(`[evaluateParameterSet] Iteration ${i + 1} failed:`, error);
		}
	}
	
	// Calculate metrics
	const averageScore = results.length > 0 
		? results.reduce((sum, r) => sum + r.total, 0) / results.length 
		: 0;
	
	const averageTime = times.length > 0 
		? times.reduce((sum, t) => sum + t, 0) / times.length 
		: 0;
	
	const successRate = successCount / iterations;
	const efficiency = averageTime > 0 ? averageScore / (averageTime / 1000) : 0;
	
	return {
		parameters,
		averageScore,
		averageTime,
		successRate,
		efficiency,
		results
	};
}

/**
 * Find optimal parameters for a specific objective
 */
export function findOptimalParameters(
	results: OptimizationResult[],
	objective: 'score' | 'time' | 'efficiency' = 'efficiency'
): OptimizationResult | null {
	if (results.length === 0) return null;
	
	switch (objective) {
		case 'score':
			return results.reduce((best, current) => 
				current.averageScore > best.averageScore ? current : best
			);
		
		case 'time':
			return results.reduce((best, current) => 
				current.averageTime < best.averageTime ? current : best
			);
		
		case 'efficiency':
		default:
			return results.reduce((best, current) => 
				current.efficiency > best.efficiency ? current : best
			);
	}
}

/**
 * Analyze parameter sensitivity
 */
export function analyzeParameterSensitivity(results: OptimizationResult[]): {
	beamWidthSensitivity: number;
	timeLimitSensitivity: number;
	pruningSensitivity: number;
	deduplicationSensitivity: number;
	maxWordsSensitivity: number;
} {
	// Calculate how much each parameter affects the efficiency
	const analysis = {
		beamWidthSensitivity: 0,
		timeLimitSensitivity: 0,
		pruningSensitivity: 0,
		deduplicationSensitivity: 0,
		maxWordsSensitivity: 0
	};
	
	// Group results by parameter values
	const beamWidthGroups = groupByParameter(results, 'beamWidth');
	const timeLimitGroups = groupByParameter(results, 'timeLimit');
	const pruningGroups = groupByParameter(results, 'usePruning');
	const deduplicationGroups = groupByParameter(results, 'useDeduplication');
	const maxWordsGroups = groupByParameter(results, 'maxWordsPerPosition');
	
	// Calculate variance in efficiency for each parameter
	analysis.beamWidthSensitivity = calculateParameterVariance(beamWidthGroups);
	analysis.timeLimitSensitivity = calculateParameterVariance(timeLimitGroups);
	analysis.pruningSensitivity = calculateParameterVariance(pruningGroups);
	analysis.deduplicationSensitivity = calculateParameterVariance(deduplicationGroups);
	analysis.maxWordsSensitivity = calculateParameterVariance(maxWordsGroups);
	
	return analysis;
}

/**
 * Group results by a specific parameter
 */
function groupByParameter(
	results: OptimizationResult[],
	parameter: keyof ParameterSet
): Map<any, OptimizationResult[]> {
	const groups = new Map();
	
	for (const result of results) {
		const value = result.parameters[parameter];
		if (!groups.has(value)) {
			groups.set(value, []);
		}
		groups.get(value).push(result);
	}
	
	return groups;
}

/**
 * Calculate variance in efficiency across parameter groups
 */
function calculateParameterVariance(groups: Map<any, OptimizationResult[]>): number {
	const groupAverages: number[] = [];
	
	for (const [, groupResults] of groups) {
		const avgEfficiency = groupResults.reduce((sum, r) => sum + r.efficiency, 0) / groupResults.length;
		groupAverages.push(avgEfficiency);
	}
	
	if (groupAverages.length <= 1) return 0;
	
	const mean = groupAverages.reduce((sum, avg) => sum + avg, 0) / groupAverages.length;
	const variance = groupAverages.reduce((sum, avg) => sum + Math.pow(avg - mean, 2), 0) / groupAverages.length;
	
	return Math.sqrt(variance); // Return standard deviation
}

/**
 * Generate parameter recommendations based on constraints
 */
export function generateParameterRecommendations(
	maxTime: number,
	minScore: number,
	memoryConstraint: 'low' | 'medium' | 'high' = 'medium'
): ParameterSet {
	// Base recommendations on constraints
	let baseParams = OPTIMIZED_PARAMETERS.BALANCED;
	
	if (maxTime <= 60000) {
		baseParams = OPTIMIZED_PARAMETERS.FAST;
	} else if (maxTime >= 300000) {
		baseParams = OPTIMIZED_PARAMETERS.QUALITY;
	}
	
	if (memoryConstraint === 'low') {
		baseParams = OPTIMIZED_PARAMETERS.MEMORY_EFFICIENT;
	}
	
	// Adjust based on constraints
	const recommendations: ParameterSet = { ...baseParams };
	
	// Adjust time limit
	recommendations.timeLimit = Math.min(maxTime, baseParams.timeLimit);
	
	// Adjust beam width based on memory constraint
	if (memoryConstraint === 'low') {
		recommendations.beamWidth = Math.min(30, recommendations.beamWidth);
	} else if (memoryConstraint === 'high') {
		recommendations.beamWidth = Math.max(80, recommendations.beamWidth);
	}
	
	return recommendations;
}

/**
 * Validate parameter set for feasibility
 */
export function validateParameters(parameters: ParameterSet): {
	valid: boolean;
	warnings: string[];
	errors: string[];
} {
	const warnings: string[] = [];
	const errors: string[] = [];
	
	// Check beam width
	if (parameters.beamWidth < 10) {
		warnings.push('Beam width is very low, may miss optimal solutions');
	} else if (parameters.beamWidth > 200) {
		warnings.push('Beam width is very high, may cause memory issues');
	}
	
	// Check time limit
	if (parameters.timeLimit < 30000) {
		warnings.push('Time limit is very short, may not find good solutions');
	} else if (parameters.timeLimit > 600000) {
		warnings.push('Time limit is very long, may be inefficient');
	}
	
	// Check max words per position
	if (parameters.maxWordsPerPosition < 20) {
		warnings.push('Max words per position is low, may miss good moves');
	} else if (parameters.maxWordsPerPosition > 200) {
		warnings.push('Max words per position is high, may slow down search');
	}
	
	// Check for invalid combinations
	if (!parameters.usePruning && !parameters.useDeduplication && parameters.beamWidth > 50) {
		warnings.push('High beam width without pruning or deduplication may cause performance issues');
	}
	
	const valid = errors.length === 0;
	
	return { valid, warnings, errors };
}
