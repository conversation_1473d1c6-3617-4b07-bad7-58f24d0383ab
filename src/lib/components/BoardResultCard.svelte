<script lang="ts">
	import type { BestLineResult } from '$lib/types';
	import Card from './ui/Card.svelte';
	import Button from './ui/Button.svelte';

	interface Props {
		date: string;
		result: BestLineResult;
		showDetails?: boolean;
		class?: string;
	}

	let { date, result, showDetails = false, class: className = '' }: Props = $props();

	// Calculate comparison metrics
	let perRoundTotal = $derived(result.perRound.reduce((sum, round) => sum + round.score, 0));
	let improvement = $derived(result.total - perRoundTotal);
	let improvementPercent = $derived(perRoundTotal > 0 ? (improvement / perRoundTotal) * 100 : 0);

	// Format date for display
	function formatDate(dateStr: string): string {
		try {
			const date = new Date(dateStr + 'T00:00:00.000Z');
			return date.toLocaleDateString('en-US', {
				weekday: 'short',
				month: 'short',
				day: 'numeric',
				year: 'numeric'
			});
		} catch {
			return dateStr;
		}
	}

	// Toggle details view
	let showDetailsState = $state(showDetails);
	function toggleDetails() {
		showDetailsState = !showDetailsState;
	}
</script>

<Card class="transition-shadow duration-200 hover:shadow-lg {className}">
	{#snippet children()}
		<!-- Header -->
		<div class="mb-4 flex items-start justify-between">
			<div>
				<h3 class="text-lg font-semibold text-gray-900">
					{formatDate(date)}
				</h3>
				<p class="text-sm text-gray-600">Letters Game Solution</p>
			</div>
			<div class="text-right">
				<div class="text-2xl font-bold text-blue-600">
					{result.total}
				</div>
				<div class="text-xs text-gray-500">Total Score</div>
			</div>
		</div>

		<!-- Quick Stats -->
		<div class="mb-4 grid grid-cols-3 gap-4">
			<div class="text-center">
				<div class="text-lg font-semibold text-gray-900">
					{result.words.length}
				</div>
				<div class="text-xs text-gray-600">Words</div>
			</div>
			<div class="text-center">
				<div class="text-lg font-semibold {improvement >= 0 ? 'text-green-600' : 'text-red-600'}">
					{improvement >= 0 ? '+' : ''}{improvement}
				</div>
				<div class="text-xs text-gray-600">vs. Greedy</div>
			</div>
			<div class="text-center">
				<div
					class="text-lg font-semibold {improvementPercent >= 0
						? 'text-green-600'
						: 'text-red-600'}"
				>
					{improvementPercent >= 0 ? '+' : ''}{improvementPercent.toFixed(1)}%
				</div>
				<div class="text-xs text-gray-600">Improvement</div>
			</div>
		</div>

		<!-- Word List Preview -->
		<div class="mb-4">
			<div class="flex flex-wrap gap-2">
				{#each result.words.slice(0, showDetailsState ? result.words.length : 3) as word}
					<span
						class="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800"
					>
						{word.toUpperCase()}
					</span>
				{/each}
				{#if !showDetailsState && result.words.length > 3}
					<span
						class="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-600"
					>
						+{result.words.length - 3} more
					</span>
				{/if}
			</div>
		</div>

		<!-- Detailed View -->
		{#if showDetailsState}
			<div class="mt-4 border-t pt-4">
				<h4 class="mb-3 text-sm font-medium text-gray-900">Greedy vs. Optimal Comparison</h4>
				<div class="space-y-2">
					{#each result.perRound as round, index}
						<div class="flex items-center justify-between text-sm">
							<div class="flex items-center space-x-3">
								<span class="w-8 text-gray-500">#{index + 1}</span>
								<span class="font-mono text-gray-700">
									{round.word.toUpperCase()}
								</span>
								<span class="text-gray-500">
									({round.score} pts)
								</span>
							</div>
							<div class="font-mono text-blue-700">
								{result.words[index]?.toUpperCase() || '-'}
							</div>
						</div>
					{/each}
				</div>
				<div class="mt-3 border-t pt-3 text-sm">
					<div class="flex justify-between">
						<span class="text-gray-600">Greedy Total:</span>
						<span class="font-medium">{perRoundTotal} points</span>
					</div>
					<div class="flex justify-between">
						<span class="text-gray-600">Optimal Total:</span>
						<span class="font-medium text-blue-600">{result.total} points</span>
					</div>
				</div>
			</div>
		{/if}

		<!-- Actions -->
		<div class="mt-4 flex items-center justify-between border-t pt-4">
			<Button variant="ghost" size="sm" onclick={toggleDetails}>
				{#snippet children()}
					{showDetailsState ? 'Hide Details' : 'Show Details'}
				{/snippet}
			</Button>
			<Button variant="outline" size="sm" href="/board/{date}">
				{#snippet children()}
					View Full Results
				{/snippet}
			</Button>
		</div>
	{/snippet}
</Card>
