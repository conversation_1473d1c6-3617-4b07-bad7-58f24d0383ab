<script lang="ts">
	interface Props {
		title?: string;
		message: string;
		type?: 'error' | 'warning' | 'info';
		showIcon?: boolean;
		class?: string;
	}

	let { title, message, type = 'error', showIcon = true, class: className = '' }: Props = $props();

	const typeClasses = {
		error: {
			container: 'bg-red-50 border-red-200 text-red-800',
			icon: 'text-red-600',
			title: 'text-red-800'
		},
		warning: {
			container: 'bg-yellow-50 border-yellow-200 text-yellow-800',
			icon: 'text-yellow-600',
			title: 'text-yellow-800'
		},
		info: {
			container: 'bg-blue-50 border-blue-200 text-blue-800',
			icon: 'text-blue-600',
			title: 'text-blue-800'
		}
	};

	let containerClasses = $derived(
		['border rounded-lg p-4', typeClasses[type].container, className].filter(Boolean).join(' ')
	);
</script>

<div class={containerClasses}>
	<div class="flex">
		{#if showIcon}
			<div class="flex-shrink-0">
				{#if type === 'error'}
					<svg
						class="h-5 w-5 {typeClasses[type].icon}"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
						/>
					</svg>
				{:else if type === 'warning'}
					<svg
						class="h-5 w-5 {typeClasses[type].icon}"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
						/>
					</svg>
				{:else}
					<svg
						class="h-5 w-5 {typeClasses[type].icon}"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
						/>
					</svg>
				{/if}
			</div>
		{/if}
		<div class="ml-3">
			{#if title}
				<h3 class="text-sm font-medium {typeClasses[type].title}">
					{title}
				</h3>
			{/if}
			<div class="text-sm {title ? 'mt-2' : ''}">
				{message}
			</div>
		</div>
	</div>
</div>
