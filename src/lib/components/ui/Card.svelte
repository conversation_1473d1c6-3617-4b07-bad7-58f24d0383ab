<script lang="ts">
	interface Props {
		class?: string;
		padding?: 'sm' | 'md' | 'lg';
		shadow?: 'sm' | 'md' | 'lg' | 'xl';
		rounded?: 'sm' | 'md' | 'lg' | 'xl';
	}

	let {
		class: className = '',
		padding = 'md',
		shadow = 'md',
		rounded = 'lg',
		children
	}: Props & { children: any } = $props();

	const paddingClasses = {
		sm: 'p-4',
		md: 'p-6',
		lg: 'p-8'
	};

	const shadowClasses = {
		sm: 'shadow-sm',
		md: 'shadow-md',
		lg: 'shadow-lg',
		xl: 'shadow-xl'
	};

	const roundedClasses = {
		sm: 'rounded-sm',
		md: 'rounded-md',
		lg: 'rounded-lg',
		xl: 'rounded-xl'
	};

	let cardClasses = $derived(
		['bg-white', paddingClasses[padding], shadowClasses[shadow], roundedClasses[rounded], className]
			.filter(Boolean)
			.join(' ')
	);
</script>

<div class={cardClasses}>
	{@render children()}
</div>
