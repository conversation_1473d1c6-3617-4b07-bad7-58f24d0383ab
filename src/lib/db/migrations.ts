/**
 * Database migration utilities for LettersBot
 * 
 * This module handles database initialization and schema migrations
 * for the Cloudflare D1 database.
 */

/**
 * D1 Database interface (Cloudflare Workers binding)
 */
interface D1Database {
	prepare(query: string): D1PreparedStatement;
	dump(): Promise<ArrayBuffer>;
	batch(statements: D1PreparedStatement[]): Promise<D1Result[]>;
	exec(query: string): Promise<D1ExecResult>;
}

interface D1PreparedStatement {
	bind(...values: any[]): D1PreparedStatement;
	first<T = any>(colName?: string): Promise<T | null>;
	run(): Promise<D1Result>;
	all<T = any>(): Promise<D1Result<T>>;
}

interface D1Result<T = any> {
	results?: T[];
	success: boolean;
	error?: string;
	meta: {
		duration: number;
		size_after: number;
		rows_read: number;
		rows_written: number;
	};
}

interface D1ExecResult {
	count: number;
	duration: number;
}

/**
 * SQL for creating the best_lines table
 */
const CREATE_BEST_LINES_TABLE = `
CREATE TABLE IF NOT EXISTS best_lines (
    date TEXT PRIMARY KEY,           -- Date in YYYY-MM-DD format
    data TEXT NOT NULL,              -- JSON-serialized BestLineResult
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
`;

/**
 * SQL for creating indexes on the best_lines table
 */
const CREATE_BEST_LINES_INDEXES = [
	`CREATE INDEX IF NOT EXISTS idx_best_lines_date ON best_lines(date);`,
	`CREATE INDEX IF NOT EXISTS idx_best_lines_created_at ON best_lines(created_at);`
];

/**
 * Initialize the database with required tables and indexes
 * This function is idempotent and safe to run multiple times
 * 
 * @param db - D1 database instance
 * @returns Promise that resolves when initialization is complete
 */
export async function initializeDatabase(db: D1Database): Promise<void> {
	try {
		console.log('Initializing LettersBot database...');

		// Create the best_lines table
		console.log('Creating best_lines table...');
		const createTableResult = await db.exec(CREATE_BEST_LINES_TABLE);
		console.log(`Table creation completed in ${createTableResult.duration}ms`);

		// Create indexes
		console.log('Creating indexes...');
		for (const indexSql of CREATE_BEST_LINES_INDEXES) {
			const indexResult = await db.exec(indexSql);
			console.log(`Index created in ${indexResult.duration}ms`);
		}

		console.log('Database initialization completed successfully');
	} catch (error) {
		console.error('Database initialization failed:', error);
		throw error;
	}
}

/**
 * Verify that the database schema is correct
 * Checks for the existence of required tables and indexes
 * 
 * @param db - D1 database instance
 * @returns Promise that resolves to true if schema is valid
 */
export async function verifyDatabaseSchema(db: D1Database): Promise<boolean> {
	try {
		console.log('Verifying database schema...');

		// Check if best_lines table exists
		const tableCheck = await db.prepare(`
			SELECT name FROM sqlite_master 
			WHERE type='table' AND name='best_lines'
		`).first();

		if (!tableCheck) {
			console.error('best_lines table does not exist');
			return false;
		}

		// Check table structure
		const tableInfo = await db.prepare(`PRAGMA table_info(best_lines)`).all();
		
		if (!tableInfo.success || !tableInfo.results) {
			console.error('Failed to get table info for best_lines');
			return false;
		}

		// Verify required columns exist
		const columns = tableInfo.results as Array<{ name: string; type: string; pk: number }>;
		const requiredColumns = ['date', 'data', 'created_at'];
		
		for (const requiredCol of requiredColumns) {
			const columnExists = columns.some(col => col.name === requiredCol);
			if (!columnExists) {
				console.error(`Required column '${requiredCol}' does not exist in best_lines table`);
				return false;
			}
		}

		// Verify primary key
		const primaryKeyCol = columns.find(col => col.pk === 1);
		if (!primaryKeyCol || primaryKeyCol.name !== 'date') {
			console.error('Primary key is not set correctly on date column');
			return false;
		}

		// Check if indexes exist
		const indexCheck = await db.prepare(`
			SELECT name FROM sqlite_master 
			WHERE type='index' AND tbl_name='best_lines'
		`).all();

		if (!indexCheck.success) {
			console.error('Failed to check indexes');
			return false;
		}

		const indexes = (indexCheck.results || []) as Array<{ name: string }>;
		const expectedIndexes = ['idx_best_lines_date', 'idx_best_lines_created_at'];
		
		for (const expectedIndex of expectedIndexes) {
			const indexExists = indexes.some(idx => idx.name === expectedIndex);
			if (!indexExists) {
				console.warn(`Index '${expectedIndex}' does not exist (this may be okay)`);
			}
		}

		console.log('Database schema verification completed successfully');
		return true;
	} catch (error) {
		console.error('Database schema verification failed:', error);
		return false;
	}
}

/**
 * Get database statistics and health information
 * Useful for monitoring and debugging
 * 
 * @param db - D1 database instance
 * @returns Promise that resolves to database statistics
 */
export async function getDatabaseStats(db: D1Database): Promise<{
	totalRecords: number;
	oldestRecord: string | null;
	newestRecord: string | null;
	databaseSize: number;
}> {
	try {
		// Get total record count
		const countResult = await db.prepare(`
			SELECT COUNT(*) as count FROM best_lines
		`).first<{ count: number }>();

		const totalRecords = countResult?.count || 0;

		// Get oldest and newest records
		const oldestResult = await db.prepare(`
			SELECT date FROM best_lines ORDER BY date ASC LIMIT 1
		`).first<{ date: string }>();

		const newestResult = await db.prepare(`
			SELECT date FROM best_lines ORDER BY date DESC LIMIT 1
		`).first<{ date: string }>();

		// Get database size (approximate)
		const sizeResult = await db.prepare(`
			SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()
		`).first<{ size: number }>();

		return {
			totalRecords,
			oldestRecord: oldestResult?.date || null,
			newestRecord: newestResult?.date || null,
			databaseSize: sizeResult?.size || 0
		};
	} catch (error) {
		console.error('Failed to get database statistics:', error);
		throw error;
	}
}

/**
 * Clean up old records beyond a certain date
 * Useful for maintaining database size within limits
 * 
 * @param db - D1 database instance
 * @param cutoffDate - Date before which records should be deleted (YYYY-MM-DD)
 * @returns Promise that resolves to the number of deleted records
 */
export async function cleanupOldRecords(
	db: D1Database,
	cutoffDate: string
): Promise<number> {
	// Validate date format
	if (!/^\d{4}-\d{2}-\d{2}$/.test(cutoffDate)) {
		throw new Error(`Invalid date format: ${cutoffDate}. Expected YYYY-MM-DD`);
	}

	try {
		console.log(`Cleaning up records older than ${cutoffDate}...`);

		const deleteResult = await db.prepare(`
			DELETE FROM best_lines WHERE date < ?
		`).bind(cutoffDate).run();

		if (!deleteResult.success) {
			throw new Error(`Delete operation failed: ${deleteResult.error || 'Unknown error'}`);
		}

		const deletedCount = deleteResult.meta.rows_written;
		console.log(`Deleted ${deletedCount} old records`);

		return deletedCount;
	} catch (error) {
		console.error(`Failed to cleanup old records before ${cutoffDate}:`, error);
		throw error;
	}
}
