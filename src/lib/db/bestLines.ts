/**
 * Database access layer for best_lines table
 *
 * This module provides functions for storing and retrieving daily solver results
 * in the Cloudflare D1 database.
 */

import type { BestLineResult, BestLineRecord } from '../types';
import { withDatabaseRetry, DEFAULT_RETRY_CONFIG, type RetryConfig } from './errorHandling';

/**
 * D1 Database interface (Cloudflare Workers binding)
 */
interface D1Database {
	prepare(query: string): D1PreparedStatement;
	dump(): Promise<ArrayBuffer>;
	batch(statements: D1PreparedStatement[]): Promise<D1Result[]>;
	exec(query: string): Promise<D1ExecResult>;
}

interface D1PreparedStatement {
	bind(...values: any[]): D1PreparedStatement;
	first<T = any>(colName?: string): Promise<T | null>;
	run(): Promise<D1Result>;
	all<T = any>(): Promise<D1Result<T>>;
}

interface D1Result<T = any> {
	results?: T[];
	success: boolean;
	error?: string;
	meta: {
		duration: number;
		size_after: number;
		rows_read: number;
		rows_written: number;
	};
}

interface D1ExecResult {
	count: number;
	duration: number;
}

/**
 * Insert or update a best line result for a specific date
 * Uses INSERT OR REPLACE for idempotent operations
 *
 * @param db - D1 database instance
 * @param date - Date in YYYY-MM-DD format
 * @param result - Best line result data
 * @param retryConfig - Optional retry configuration
 * @returns Promise that resolves when the operation completes
 */
export async function insertBestLine(
	db: D1Database,
	date: string,
	result: BestLineResult,
	retryConfig: RetryConfig = DEFAULT_RETRY_CONFIG
): Promise<void> {
	// Validate date format
	if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
		throw new Error(`Invalid date format: ${date}. Expected YYYY-MM-DD`);
	}

	return withDatabaseRetry(
		async () => {
			// Serialize the result data
			const dataJson = JSON.stringify(result);

			// Prepare the INSERT OR REPLACE statement
			const stmt = db.prepare(`
			INSERT OR REPLACE INTO best_lines (date, data, created_at)
			VALUES (?, ?, CURRENT_TIMESTAMP)
		`);

			const dbResult = await stmt.bind(date, dataJson).run();

			if (!dbResult.success) {
				throw new Error(`Database operation failed: ${dbResult.error || 'Unknown error'}`);
			}

			console.log(`Successfully inserted/updated best line for date: ${date}`);
		},
		`insertBestLine(${date})`,
		retryConfig
	);
}

/**
 * Retrieve the best line result for a specific date
 *
 * @param db - D1 database instance
 * @param date - Date in YYYY-MM-DD format
 * @param retryConfig - Optional retry configuration
 * @returns Promise that resolves to the best line result or null if not found
 */
export async function getBestLine(
	db: D1Database,
	date: string,
	retryConfig: RetryConfig = DEFAULT_RETRY_CONFIG
): Promise<BestLineResult | null> {
	// Validate date format
	if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
		throw new Error(`Invalid date format: ${date}. Expected YYYY-MM-DD`);
	}

	return withDatabaseRetry(
		async () => {
			// Prepare the SELECT statement
			const stmt = db.prepare(`
			SELECT data, created_at
			FROM best_lines
			WHERE date = ?
		`);

			const record = await stmt.bind(date).first<BestLineRecord>();

			if (!record) {
				return null;
			}

			// Parse the JSON data
			try {
				const result: BestLineResult = JSON.parse(record.data);
				return result;
			} catch (parseError) {
				console.error(`Failed to parse JSON data for date ${date}:`, parseError);
				throw new Error(`Invalid JSON data in database for date ${date}`);
			}
		},
		`getBestLine(${date})`,
		retryConfig
	);
}

/**
 * Get all best line results within a date range
 * Useful for historical analysis and bulk operations
 *
 * @param db - D1 database instance
 * @param startDate - Start date in YYYY-MM-DD format (inclusive)
 * @param endDate - End date in YYYY-MM-DD format (inclusive)
 * @returns Promise that resolves to an array of date-result pairs
 */
export async function getBestLinesInRange(
	db: D1Database,
	startDate: string,
	endDate: string
): Promise<Array<{ date: string; result: BestLineResult; createdAt: string }>> {
	// Validate date formats
	if (!/^\d{4}-\d{2}-\d{2}$/.test(startDate) || !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
		throw new Error('Invalid date format. Expected YYYY-MM-DD');
	}

	// Prepare the SELECT statement
	const stmt = db.prepare(`
		SELECT date, data, created_at
		FROM best_lines
		WHERE date >= ? AND date <= ?
		ORDER BY date ASC
	`);

	try {
		const dbResult = await stmt.bind(startDate, endDate).all<BestLineRecord & { date: string }>();

		if (!dbResult.success) {
			throw new Error(`Database query failed: ${dbResult.error || 'Unknown error'}`);
		}

		const results = dbResult.results || [];

		return results.map((record) => {
			try {
				const result: BestLineResult = JSON.parse(record.data);
				return {
					date: record.date,
					result,
					createdAt: record.created_at
				};
			} catch (parseError) {
				console.error(`Failed to parse JSON data for date ${record.date}:`, parseError);
				throw new Error(`Invalid JSON data in database for date ${record.date}`);
			}
		});
	} catch (error) {
		console.error(`Failed to retrieve best lines in range ${startDate} to ${endDate}:`, error);
		throw error;
	}
}

/**
 * Get the most recent N best line results
 * Useful for displaying recent history
 *
 * @param db - D1 database instance
 * @param limit - Maximum number of results to return
 * @returns Promise that resolves to an array of recent results
 */
export async function getRecentBestLines(
	db: D1Database,
	limit: number = 30
): Promise<Array<{ date: string; result: BestLineResult; createdAt: string }>> {
	if (limit <= 0 || limit > 1000) {
		throw new Error('Limit must be between 1 and 1000');
	}

	// Prepare the SELECT statement
	const stmt = db.prepare(`
		SELECT date, data, created_at
		FROM best_lines
		ORDER BY date DESC
		LIMIT ?
	`);

	try {
		const dbResult = await stmt.bind(limit).all<BestLineRecord & { date: string }>();

		if (!dbResult.success) {
			throw new Error(`Database query failed: ${dbResult.error || 'Unknown error'}`);
		}

		const results = dbResult.results || [];

		return results.map((record) => {
			try {
				const result: BestLineResult = JSON.parse(record.data);
				return {
					date: record.date,
					result,
					createdAt: record.created_at
				};
			} catch (parseError) {
				console.error(`Failed to parse JSON data for date ${record.date}:`, parseError);
				throw new Error(`Invalid JSON data in database for date ${record.date}`);
			}
		});
	} catch (error) {
		console.error(`Failed to retrieve recent best lines:`, error);
		throw error;
	}
}

/**
 * Check if a best line exists for a specific date
 * Useful for conditional operations
 *
 * @param db - D1 database instance
 * @param date - Date in YYYY-MM-DD format
 * @returns Promise that resolves to true if the record exists
 */
export async function bestLineExists(db: D1Database, date: string): Promise<boolean> {
	// Validate date format
	if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
		throw new Error(`Invalid date format: ${date}. Expected YYYY-MM-DD`);
	}

	// Prepare the SELECT statement
	const stmt = db.prepare(`
		SELECT 1
		FROM best_lines
		WHERE date = ?
		LIMIT 1
	`);

	try {
		const result = await stmt.bind(date).first();
		return result !== null;
	} catch (error) {
		console.error(`Failed to check if best line exists for date ${date}:`, error);
		throw error;
	}
}
