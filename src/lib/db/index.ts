/**
 * Database module exports
 *
 * This module contains database access functions for storing and retrieving
 * best line results in Cloudflare D1.
 */

// Best lines database operations
export {
	insertBestLine,
	getBestLine,
	getBestLinesInRange,
	getRecentBestLines,
	bestLineExists
} from './bestLines';

// Database initialization and migration utilities
export {
	initializeDatabase,
	verifyDatabaseSchema,
	getDatabaseStats,
	cleanupOldRecords
} from './migrations';

// Database error handling and retry utilities
export {
	withDatabaseRetry,
	DatabaseError,
	DatabaseErrorType,
	DEFAULT_RETRY_CONFIG,
	getDatabaseStats as getErrorStats,
	resetDatabaseStats,
	checkDatabaseHealth,
	type RetryConfig
} from './errorHandling';
