/**
 * Database error handling and retry logic for LettersBot
 * 
 * This module provides robust error handling, retry mechanisms, and recovery
 * strategies for Cloudflare D1 database operations.
 */

/**
 * Database error types
 */
export enum DatabaseErrorType {
	CONNECTION_FAILED = 'CONNECTION_FAILED',
	QUERY_TIMEOUT = 'QUERY_TIMEOUT',
	CONSTRAINT_VIOLATION = 'CONSTRAINT_VIOLATION',
	INVALID_QUERY = 'INVALID_QUERY',
	TRANSACTION_FAILED = 'TRANSACTION_FAILED',
	QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
	UNKNOWN = 'UNKNOWN'
}

/**
 * Custom database error class
 */
export class DatabaseError extends Error {
	constructor(
		public readonly type: DatabaseErrorType,
		message: string,
		public readonly originalError?: Error,
		public readonly context?: Record<string, any>
	) {
		super(message);
		this.name = 'DatabaseError';
	}

	/**
	 * Check if this error is recoverable with retry
	 */
	get isRecoverable(): boolean {
		switch (this.type) {
			case DatabaseErrorType.CONNECTION_FAILED:
			case DatabaseErrorType.QUERY_TIMEOUT:
			case DatabaseErrorType.TRANSACTION_FAILED:
				return true;
			case DatabaseErrorType.CONSTRAINT_VIOLATION:
			case DatabaseErrorType.INVALID_QUERY:
			case DatabaseErrorType.QUOTA_EXCEEDED:
				return false;
			case DatabaseErrorType.UNKNOWN:
				return true; // Assume unknown errors might be recoverable
			default:
				return false;
		}
	}
}

/**
 * Configuration for retry operations
 */
export interface RetryConfig {
	/** Maximum number of retry attempts */
	maxRetries: number;
	/** Initial delay between retries in milliseconds */
	initialDelay: number;
	/** Maximum delay between retries in milliseconds */
	maxDelay: number;
	/** Multiplier for exponential backoff */
	backoffMultiplier: number;
	/** Whether to add random jitter to delays */
	useJitter: boolean;
	/** Timeout for individual operations in milliseconds */
	operationTimeout: number;
}

/**
 * Default retry configuration
 */
export const DEFAULT_RETRY_CONFIG: RetryConfig = {
	maxRetries: 3,
	initialDelay: 100,
	maxDelay: 5000,
	backoffMultiplier: 2,
	useJitter: true,
	operationTimeout: 10000
};

/**
 * Statistics for database operations
 */
export interface DatabaseStats {
	totalOperations: number;
	successfulOperations: number;
	failedOperations: number;
	retriedOperations: number;
	averageResponseTime: number;
	errorsByType: Map<DatabaseErrorType, number>;
}

/**
 * Global database statistics
 */
const dbStats: DatabaseStats = {
	totalOperations: 0,
	successfulOperations: 0,
	failedOperations: 0,
	retriedOperations: 0,
	averageResponseTime: 0,
	errorsByType: new Map()
};

/**
 * Classify an error into a specific database error type
 */
function classifyError(error: any): DatabaseErrorType {
	if (!error) return DatabaseErrorType.UNKNOWN;

	const message = error.message?.toLowerCase() || '';
	const errorString = error.toString?.()?.toLowerCase() || '';

	// Check for specific error patterns
	if (message.includes('timeout') || message.includes('timed out')) {
		return DatabaseErrorType.QUERY_TIMEOUT;
	}

	if (message.includes('connection') || message.includes('network')) {
		return DatabaseErrorType.CONNECTION_FAILED;
	}

	if (message.includes('constraint') || message.includes('unique') || message.includes('foreign key')) {
		return DatabaseErrorType.CONSTRAINT_VIOLATION;
	}

	if (message.includes('syntax') || message.includes('invalid') || message.includes('malformed')) {
		return DatabaseErrorType.INVALID_QUERY;
	}

	if (message.includes('quota') || message.includes('limit') || message.includes('exceeded')) {
		return DatabaseErrorType.QUOTA_EXCEEDED;
	}

	if (message.includes('transaction') || message.includes('rollback')) {
		return DatabaseErrorType.TRANSACTION_FAILED;
	}

	return DatabaseErrorType.UNKNOWN;
}

/**
 * Calculate delay for retry with exponential backoff and jitter
 */
function calculateDelay(attempt: number, config: RetryConfig): number {
	const exponentialDelay = config.initialDelay * Math.pow(config.backoffMultiplier, attempt);
	const cappedDelay = Math.min(exponentialDelay, config.maxDelay);
	
	if (config.useJitter) {
		// Add random jitter (±25% of the delay)
		const jitter = cappedDelay * 0.25 * (Math.random() * 2 - 1);
		return Math.max(0, cappedDelay + jitter);
	}
	
	return cappedDelay;
}

/**
 * Update database statistics
 */
function updateStats(success: boolean, responseTime: number, errorType?: DatabaseErrorType, wasRetried = false): void {
	dbStats.totalOperations++;
	
	if (success) {
		dbStats.successfulOperations++;
	} else {
		dbStats.failedOperations++;
		if (errorType) {
			const currentCount = dbStats.errorsByType.get(errorType) || 0;
			dbStats.errorsByType.set(errorType, currentCount + 1);
		}
	}
	
	if (wasRetried) {
		dbStats.retriedOperations++;
	}
	
	// Update average response time (simple moving average)
	dbStats.averageResponseTime = (dbStats.averageResponseTime * (dbStats.totalOperations - 1) + responseTime) / dbStats.totalOperations;
}

/**
 * Execute a database operation with retry logic and error handling
 */
export async function withDatabaseRetry<T>(
	operation: () => Promise<T>,
	operationName: string,
	config: RetryConfig = DEFAULT_RETRY_CONFIG
): Promise<T> {
	const startTime = Date.now();
	let lastError: DatabaseError | null = null;
	let wasRetried = false;

	for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
		try {
			// Add operation timeout
			const timeoutPromise = new Promise<never>((_, reject) => {
				setTimeout(() => {
					reject(new DatabaseError(
						DatabaseErrorType.QUERY_TIMEOUT,
						`Operation ${operationName} timed out after ${config.operationTimeout}ms`
					));
				}, config.operationTimeout);
			});

			const result = await Promise.race([operation(), timeoutPromise]);
			
			// Operation succeeded
			const responseTime = Date.now() - startTime;
			updateStats(true, responseTime, undefined, wasRetried);
			
			if (attempt > 0) {
				console.log(`[DB] Operation ${operationName} succeeded after ${attempt} retries`);
			}
			
			return result;

		} catch (error) {
			const errorType = classifyError(error);
			const dbError = new DatabaseError(
				errorType,
				`Database operation ${operationName} failed: ${error.message || 'Unknown error'}`,
				error instanceof Error ? error : undefined,
				{ attempt, operationName }
			);

			lastError = dbError;

			// Log the error
			console.error(`[DB] Operation ${operationName} failed (attempt ${attempt + 1}/${config.maxRetries + 1}):`, {
				type: errorType,
				message: dbError.message,
				recoverable: dbError.isRecoverable
			});

			// Check if we should retry
			if (attempt < config.maxRetries && dbError.isRecoverable) {
				wasRetried = true;
				const delay = calculateDelay(attempt, config);
				console.log(`[DB] Retrying ${operationName} in ${delay}ms...`);
				await new Promise(resolve => setTimeout(resolve, delay));
				continue;
			}

			// No more retries or error is not recoverable
			const responseTime = Date.now() - startTime;
			updateStats(false, responseTime, errorType, wasRetried);
			throw dbError;
		}
	}

	// This should never be reached, but TypeScript requires it
	throw lastError || new DatabaseError(DatabaseErrorType.UNKNOWN, 'Unexpected error in retry logic');
}

/**
 * Get current database statistics
 */
export function getDatabaseStats(): DatabaseStats {
	return {
		...dbStats,
		errorsByType: new Map(dbStats.errorsByType)
	};
}

/**
 * Reset database statistics
 */
export function resetDatabaseStats(): void {
	dbStats.totalOperations = 0;
	dbStats.successfulOperations = 0;
	dbStats.failedOperations = 0;
	dbStats.retriedOperations = 0;
	dbStats.averageResponseTime = 0;
	dbStats.errorsByType.clear();
}

/**
 * Check database health based on recent statistics
 */
export function checkDatabaseHealth(): {
	healthy: boolean;
	issues: string[];
	successRate: number;
	retryRate: number;
} {
	const issues: string[] = [];
	const successRate = dbStats.totalOperations > 0 
		? dbStats.successfulOperations / dbStats.totalOperations 
		: 1;
	const retryRate = dbStats.totalOperations > 0 
		? dbStats.retriedOperations / dbStats.totalOperations 
		: 0;

	// Check success rate
	if (successRate < 0.95) {
		issues.push(`Low success rate: ${(successRate * 100).toFixed(1)}%`);
	}

	// Check retry rate
	if (retryRate > 0.1) {
		issues.push(`High retry rate: ${(retryRate * 100).toFixed(1)}%`);
	}

	// Check average response time
	if (dbStats.averageResponseTime > 5000) {
		issues.push(`High average response time: ${dbStats.averageResponseTime.toFixed(0)}ms`);
	}

	// Check for specific error patterns
	const quotaErrors = dbStats.errorsByType.get(DatabaseErrorType.QUOTA_EXCEEDED) || 0;
	if (quotaErrors > 0) {
		issues.push(`Quota exceeded errors: ${quotaErrors}`);
	}

	const healthy = issues.length === 0;

	return {
		healthy,
		issues,
		successRate,
		retryRate
	};
}
