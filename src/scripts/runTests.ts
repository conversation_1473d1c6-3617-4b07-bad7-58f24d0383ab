/**
 * Test runner script for the LettersBot solver
 * 
 * This script runs comprehensive tests to validate the solver implementation.
 */

import { runAllSolverTests } from '../lib/solver/tests/solverTests';

async function main() {
	console.log('🚀 LettersBot Solver Test Suite');
	console.log('================================\n');
	
	try {
		// Load dictionary first
		console.log('📚 Loading dictionary...');
		try {
			const fs = await import('fs');
			const path = await import('path');
			
			const dictPath = path.join(process.cwd(), 'src/lib/dict.bin');
			if (fs.existsSync(dictPath)) {
				const dictData = fs.readFileSync(dictPath);
				const { loadDictionary } = await import('../lib/utils/dictionary');
				await loadDictionary(dictData.buffer);
				console.log('✅ Dictionary loaded successfully\n');
			} else {
				console.log('⚠️  Dictionary not found, some tests may use fallbacks\n');
			}
		} catch (error) {
			console.log('⚠️  Could not load dictionary, continuing with tests\n');
		}
		
		// Run all tests
		const results = await runAllSolverTests();
		
		// Print summary
		console.log('\n' + '='.repeat(50));
		console.log('📋 FINAL TEST SUMMARY');
		console.log('='.repeat(50));
		
		if (results.failedTests === 0) {
			console.log('🎉 ALL TESTS PASSED! 🎉');
			console.log(`✅ ${results.passedTests}/${results.totalTests} tests successful`);
		} else {
			console.log('❌ SOME TESTS FAILED');
			console.log(`✅ ${results.passedTests}/${results.totalTests} tests passed`);
			console.log(`❌ ${results.failedTests}/${results.totalTests} tests failed`);
			
			console.log('\n🔍 Failed Tests:');
			results.results
				.filter(r => !r.passed)
				.forEach(r => {
					console.log(`   • ${r.name}: ${r.error}`);
				});
		}
		
		console.log(`⏱️  Total execution time: ${results.totalDuration}ms`);
		console.log('='.repeat(50));
		
		// Exit with appropriate code
		process.exit(results.failedTests === 0 ? 0 : 1);
		
	} catch (error) {
		console.error('💥 Test runner failed:', error);
		process.exit(1);
	}
}

// Run the tests
main().catch(error => {
	console.error('💥 Unexpected error:', error);
	process.exit(1);
});
