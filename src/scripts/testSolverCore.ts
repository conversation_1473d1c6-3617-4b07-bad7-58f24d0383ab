/**
 * Core solver component test
 * 
 * This script tests the core solver components without the potentially
 * slow bestWords function to validate the fundamental algorithms.
 */

import { Board } from '../lib/models/Board';
import { Tile } from '../lib/models/Tile';
import { Word } from '../lib/models/Word';
import { GameState } from '../lib/models/GameState';
import { upperBoundForBoard } from '../lib/solver/optimization';
import { hashBoard, BoardHashSet } from '../lib/solver/hashing';
import { Hungarian, solveAssignment } from '../lib/solver/hungarian';
import { OPTIMIZED_PARAMETERS } from '../lib/solver/parameterOptimization';

async function main() {
	console.log('⚡ LettersBot Core Solver Test');
	console.log('==============================\n');
	
	try {
		// Test 1: Board and Word operations
		await testBoardOperations();
		
		// Test 2: Game state transitions
		await testGameStateTransitions();
		
		// Test 3: Hashing performance
		await testHashingPerformance();
		
		// Test 4: Upper bound calculations
		await testUpperBounds();
		
		// Test 5: Hungarian algorithm
		await testHungarianAlgorithm();
		
		// Test 6: Parameter validation
		await testParameterValidation();
		
		console.log('\n🎉 All core solver tests completed successfully!');
		console.log('\n📊 Summary:');
		console.log('   ✅ Board operations working correctly');
		console.log('   ✅ Game state transitions functional');
		console.log('   ✅ Hashing system performing well');
		console.log('   ✅ Upper bound calculations working');
		console.log('   ✅ Hungarian algorithm operational');
		console.log('   ✅ Parameter validation working');
		console.log('\n🚀 The solver core is ready for deployment!');
		
	} catch (error) {
		console.error('💥 Core solver test failed:', error);
		process.exit(1);
	}
}

async function testBoardOperations(): Promise<void> {
	console.log('🎲 Testing board operations...');
	
	// Create a test board
	const board = createTestBoard();
	console.log('   Created test board:');
	console.log('   ' + board.toString().split('\n').join('\n   '));
	
	// Test board cloning
	const cloned = board.clone();
	if (!cloned) throw new Error('Board cloning failed');
	
	// Test tile access
	const tile = board.getTile(0, 0);
	if (!tile) throw new Error('Tile access failed');
	console.log(`   Tile at [0,0]: ${tile.letter} (${tile.base} points)`);
	
	// Test word creation and scoring
	const word = new Word('CAT', [[0, 0], [0, 1], [0, 2]], 0);
	const score = word.calculateScore(board);
	console.log(`   Word "CAT" scores: ${score} points`);
	
	// Test word application
	const newBoard = board.apply(word);
	console.log('   Applied word to board successfully');
	
	console.log('   ✅ Board operations test passed\n');
}

async function testGameStateTransitions(): Promise<void> {
	console.log('🎮 Testing game state transitions...');
	
	const board = createTestBoard();
	let gameState = GameState.fromBoard(board);
	
	console.log(`   Initial state: Turn ${gameState.turn}, Score ${gameState.total}`);
	
	// Create some test words
	const testWords = [
		new Word('CAT', [[0, 0], [0, 1], [0, 2]]),
		new Word('DOG', [[1, 0], [1, 1], [1, 2]]),
		new Word('TEST', [[2, 0], [2, 1], [2, 2], [2, 3]])
	];
	
	for (let i = 0; i < testWords.length && gameState.canPlayMove(); i++) {
		const word = testWords[i];
		try {
			const newState = gameState.playMove(word);
			console.log(`   Turn ${newState.turn}: Played "${word.letters}" for ${word.score} points (Total: ${newState.total})`);
			gameState = newState;
		} catch (error) {
			console.log(`   Turn ${i + 1}: Could not play "${word.letters}" - continuing with next word`);
		}
	}
	
	console.log(`   Final state: Turn ${gameState.turn}, Score ${gameState.total}`);
	console.log('   ✅ Game state transitions test passed\n');
}

async function testHashingPerformance(): Promise<void> {
	console.log('🔗 Testing hashing performance...');
	
	const boards: Board[] = [];
	for (let i = 0; i < 1000; i++) {
		boards.push(Board.createRandom());
	}
	
	const startTime = Date.now();
	const hashes = boards.map(board => hashBoard(board));
	const endTime = Date.now();
	
	const uniqueHashes = new Set(hashes).size;
	const collisionRate = (boards.length - uniqueHashes) / boards.length;
	const avgTime = (endTime - startTime) / boards.length;
	
	console.log(`   Hashed ${boards.length} boards in ${endTime - startTime}ms`);
	console.log(`   Average time per hash: ${avgTime.toFixed(3)}ms`);
	console.log(`   Unique hashes: ${uniqueHashes}/${boards.length}`);
	console.log(`   Collision rate: ${(collisionRate * 100).toFixed(2)}%`);
	
	// Test hash set operations
	const hashSet = new BoardHashSet();
	const testBoard = boards[0];
	hashSet.add(testBoard);
	
	if (!hashSet.has(testBoard)) {
		throw new Error('Hash set should contain added board');
	}
	
	if (!hashSet.has(testBoard.clone())) {
		throw new Error('Hash set should contain cloned board');
	}
	
	console.log('   ✅ Hashing performance test passed\n');
}

async function testUpperBounds(): Promise<void> {
	console.log('📊 Testing upper bound calculations...');
	
	const boards = [
		createTestBoard(),
		Board.createRandom(),
		createHighValueBoard(),
		createLowValueBoard()
	];
	
	const boardNames = ['Test Board', 'Random Board', 'High Value Board', 'Low Value Board'];
	
	for (let i = 0; i < boards.length; i++) {
		const board = boards[i];
		const remainingTurns = 3;
		
		const startTime = Date.now();
		const upperBound = upperBoundForBoard(board, remainingTurns);
		const endTime = Date.now();
		
		console.log(`   ${boardNames[i]}: Upper bound = ${upperBound} (${endTime - startTime}ms)`);
		
		if (upperBound < 0) {
			throw new Error(`Invalid upper bound: ${upperBound}`);
		}
	}
	
	console.log('   ✅ Upper bound calculations test passed\n');
}

async function testHungarianAlgorithm(): Promise<void> {
	console.log('🧮 Testing Hungarian algorithm...');
	
	// Test with a simple 3x3 matrix
	const costMatrix = [
		[4, 1, 3],
		[2, 0, 5],
		[3, 2, 2]
	];
	
	const result = solveAssignment(costMatrix);
	console.log(`   3x3 matrix: Cost = ${result.cost}, Success = ${result.success}`);
	
	if (!result.success) throw new Error('Hungarian algorithm failed on 3x3 matrix');
	
	// Test with larger matrix
	const size = 10;
	const largeCostMatrix = Array(size).fill(null).map(() => 
		Array(size).fill(null).map(() => Math.floor(Math.random() * 100))
	);
	
	const startTime = Date.now();
	const largeResult = solveAssignment(largeCostMatrix);
	const endTime = Date.now();
	
	console.log(`   ${size}x${size} matrix: Cost = ${largeResult.cost}, Time = ${endTime - startTime}ms`);
	
	if (!largeResult.success) throw new Error(`Hungarian algorithm failed on ${size}x${size} matrix`);
	
	console.log('   ✅ Hungarian algorithm test passed\n');
}

async function testParameterValidation(): Promise<void> {
	console.log('⚙️  Testing parameter validation...');
	
	const { validateParameters } = await import('../lib/solver/parameterOptimization');
	
	for (const [name, params] of Object.entries(OPTIMIZED_PARAMETERS)) {
		const validation = validateParameters(params);
		
		if (!validation.valid) {
			throw new Error(`Invalid ${name} parameters: ${validation.errors.join(', ')}`);
		}
		
		console.log(`   ${name}: Valid (${validation.warnings.length} warnings)`);
	}
	
	console.log('   ✅ Parameter validation test passed\n');
}

function createTestBoard(): Board {
	const tiles: Tile[][] = [];
	const letters = [
		['C', 'A', 'T', 'S', 'E'],
		['H', 'O', 'U', 'S', 'E'],
		['W', 'O', 'R', 'D', 'S'],
		['G', 'A', 'M', 'E', 'S'],
		['T', 'E', 'S', 'T', 'S']
	];
	
	for (let row = 0; row < 5; row++) {
		tiles[row] = [];
		for (let col = 0; col < 5; col++) {
			let letterMult: 1|2|3 = 1;
			let wordMult: 1|2|3 = 1;
			
			if ((row === 0 || row === 4) && (col === 0 || col === 4)) {
				wordMult = 2;
			}
			if (row === 2 && col === 2) {
				letterMult = 3;
			}
			
			tiles[row][col] = new Tile(
				letters[row][col],
				row as 0|1|2|3|4,
				col as 0|1|2|3|4,
				letterMult,
				wordMult
			);
		}
	}
	
	return new Board(tiles);
}

function createHighValueBoard(): Board {
	const tiles: Tile[][] = [];
	const highValueLetters = ['Q', 'X', 'Z', 'J', 'K'];
	
	for (let row = 0; row < 5; row++) {
		tiles[row] = [];
		for (let col = 0; col < 5; col++) {
			const letter = highValueLetters[(row * 5 + col) % highValueLetters.length];
			tiles[row][col] = new Tile(
				letter,
				row as 0|1|2|3|4,
				col as 0|1|2|3|4,
				3,
				3
			);
		}
	}
	
	return new Board(tiles);
}

function createLowValueBoard(): Board {
	const tiles: Tile[][] = [];
	
	for (let row = 0; row < 5; row++) {
		tiles[row] = [];
		for (let col = 0; col < 5; col++) {
			tiles[row][col] = new Tile(
				'A',
				row as 0|1|2|3|4,
				col as 0|1|2|3|4,
				1,
				1
			);
		}
	}
	
	return new Board(tiles);
}

// Run the core tests
main().catch(error => {
	console.error('💥 Unexpected error:', error);
	process.exit(1);
});
