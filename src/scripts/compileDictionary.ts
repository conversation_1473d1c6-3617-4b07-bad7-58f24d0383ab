// scripts/compileDictionary.ts
//------------------------------------------------------------
//  Compile a plain‑text word list (one word per line) into a
//  compact binary file used by LettersBot at runtime.
//
//  Usage:
//      npx ts-node scripts/compileDictionary.ts words.txt dict.bin
//
//  Record layout (little‑endian):
//      [header]
//          uint32  nEntries
//      nEntries × {
//          uint16  wordByteLen      // UTF‑8 length
//          uint16  letterScoreSum   // Σ Scrabble scores (A=1, B=3…)
//          uint8   wordLen          // 2 – 25 inclusive
//          uint8×26 letterFreq      // histogram for A…Z
//          bytes   utf8Word         // actual bytes
//      }
//------------------------------------------------------------
import { readFileSync, writeFileSync } from 'node:fs';
import { basename } from 'node:path';

//------------------------------------------------------------
// 1.  Parse CLI args
//------------------------------------------------------------
const [, , inPath = 'data/en.txt', outPath = 'src/lib/dict.bin'] = process.argv;
console.log(`[compileDictionary] input → ${basename(inPath)}, output → ${basename(outPath)}`);

//------------------------------------------------------------
// 2.  Letter score table (Scrabble-style)
//------------------------------------------------------------
const LETTER_SCORES: Record<string, number> = {
	A: 1,
	B: 3,
	C: 3,
	D: 2,
	E: 1,
	F: 4,
	G: 2,
	H: 4,
	I: 1,
	J: 8,
	K: 5,
	L: 1,
	M: 3,
	N: 1,
	O: 1,
	P: 3,
	Q: 10,
	R: 1,
	S: 1,
	T: 1,
	U: 1,
	V: 4,
	W: 4,
	X: 8,
	Y: 4,
	Z: 10
};

//------------------------------------------------------------
// 3.  Read word list & build binary array
//------------------------------------------------------------
const lines = readFileSync(inPath, 'utf8').split(/\r?\n/);
const chunks: number[] = [];
let entryCount = 0;

for (const raw of lines) {
	const word = raw.trim().toUpperCase();
	if (word.length < 2 || word.length > 25 || /[^A-Z]/.test(word)) continue;

	// 3a. Histogram & base score
	const hist = new Uint8Array(26);
	let score = 0;
	for (const ch of word) {
		const idx = ch.charCodeAt(0) - 65;
		hist[idx]++;
		score += LETTER_SCORES[ch];
	}

	// 3b. Serialise record header
	const utf8 = new TextEncoder().encode(word);
	const len = utf8.length;

	chunks.push(len & 0xff, len >>> 8); // uint16 wordByteLen
	chunks.push(score & 0xff, score >>> 8); // uint16 letterScoreSum
	chunks.push(word.length); // uint8 wordLen
	for (let i = 0; i < 26; i++) chunks.push(hist[i]); // 26‑byte histogram
	chunks.push(...utf8); // word bytes

	entryCount++;
}

//------------------------------------------------------------
// 4.  Prepend header & flush to disk
//------------------------------------------------------------
const header = new Uint8Array(4);
new DataView(header.buffer).setUint32(0, entryCount, true);
writeFileSync(outPath, Buffer.concat([header, Buffer.from(chunks)]));

console.log(
	`[compileDictionary] wrote ${entryCount} entries → ${(chunks.length + 4).toLocaleString()} bytes`
);
