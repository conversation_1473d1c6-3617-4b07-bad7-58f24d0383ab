/**
 * Test script for word playing functionality on the live Letters game website
 * 
 * This script tests the ability to click tiles and submit words on the live game.
 */

import { chromium, type Browser, type Page } from '@playwright/test';
import { scrapeBoard, injectHelperFunctions } from '../lib/solver/scraping';
import { playWord } from '../lib/solver/gameInteraction';
import { Word } from '../lib/models/Word';

async function main() {
	console.log('🎮 Testing Word Playing on Live Website');
	console.log('======================================\n');

	let browser: Browser | null = null;
	let page: Page | null = null;

	try {
		// Launch browser
		console.log('1. Launching browser...');
		browser = await chromium.launch({ 
			headless: false, // Keep visible for debugging
			slowMo: 1000 // Slow down actions for visibility
		});
		
		page = await browser.newPage();
		page.setDefaultTimeout(30000);

		// Navigate to the Letters game
		console.log('2. Navigating to Letters game...');
		await page.goto('https://play.thelettersgame.com/', { waitUntil: 'networkidle' });
		await page.waitForTimeout(3000);

		console.log('3. Looking for "Play on Web" button...');
		const playButton = await page.$('button:has-text("Play on Web")');
		if (playButton) {
			console.log('   ✅ Found "Play on Web" button, clicking...');
			await playButton.click();
			await page.waitForTimeout(5000);
		}

		// Inject helper functions
		console.log('4. Injecting helper functions...');
		await injectHelperFunctions(page);

		// Scrape the board
		console.log('5. Scraping board...');
		const board = await scrapeBoard(page);
		
		console.log('   📊 Current board state:');
		for (let row = 0; row < 5; row++) {
			const rowStr = board.tiles[row].map(tile => 
				`${tile.letter}${tile.letterMult > 1 ? `(L${tile.letterMult})` : ''}${tile.wordMult > 1 ? `(W${tile.wordMult})` : ''}`
			).join(' ');
			console.log(`      ${rowStr}`);
		}

		// Find a simple word manually from the board
		console.log('6. Looking for simple words to play...');
		const simpleWords = findSimpleWords(board);
		
		if (simpleWords.length === 0) {
			console.log('   ⚠️  No simple words found to test with');
			return;
		}

		console.log(`   🎯 Found ${simpleWords.length} simple words to test:`);
		simpleWords.forEach((word, index) => {
			console.log(`      ${index + 1}. ${word.letters} at positions ${word.positions.map(p => `[${p[0]},${p[1]}]`).join('-')}`);
		});

		// Test playing the first word
		const wordToPlay = simpleWords[0];
		console.log(`\n7. Testing word playing with: ${wordToPlay.letters}`);
		
		// Take screenshot before playing
		await page.screenshot({ path: 'before-word-play.png', fullPage: true });
		console.log('   📸 Screenshot taken before playing word');

		// Try to play the word
		console.log('   🎮 Attempting to play word...');
		await testWordPlayingProcess(page, wordToPlay);

		// Take screenshot after playing
		await page.screenshot({ path: 'after-word-play.png', fullPage: true });
		console.log('   📸 Screenshot taken after playing word');

		console.log('\n✅ Word playing test completed!');
		
	} catch (error) {
		console.error('❌ Error during testing:', error);
		console.error('Stack trace:', (error as Error).stack);
		
		if (page) {
			try {
				await page.screenshot({ path: 'word-play-error.png', fullPage: true });
				console.log('   📸 Error screenshot saved');
			} catch (screenshotError) {
				console.error('Failed to take error screenshot:', screenshotError);
			}
		}
	} finally {
		if (browser) {
			console.log('\n8. Keeping browser open for inspection...');
			console.log('   Press Ctrl+C to close when done inspecting');
			// Keep browser open for manual inspection
			await new Promise(resolve => setTimeout(resolve, 60000)); // Wait 1 minute
			await browser.close();
		}
	}
}

function findSimpleWords(board: any): Word[] {
	const words: Word[] = [];
	
	// Look for simple 2-3 letter words in rows and columns
	const simpleWordPatterns = ['IT', 'TO', 'GO', 'NO', 'ON', 'IN', 'UP', 'AT', 'BE', 'OR', 'AN', 'IF'];
	
	// Check horizontal words
	for (let row = 0; row < 5; row++) {
		for (let col = 0; col < 4; col++) {
			// Check 2-letter words
			const twoLetter = board.tiles[row][col].letter + board.tiles[row][col + 1].letter;
			if (simpleWordPatterns.includes(twoLetter)) {
				words.push(new Word(twoLetter, [[row, col], [row, col + 1]]));
			}
			
			// Check 3-letter words
			if (col < 3) {
				const threeLetter = board.tiles[row][col].letter + board.tiles[row][col + 1].letter + board.tiles[row][col + 2].letter;
				if (simpleWordPatterns.includes(threeLetter)) {
					words.push(new Word(threeLetter, [[row, col], [row, col + 1], [row, col + 2]]));
				}
			}
		}
	}
	
	// Check vertical words
	for (let col = 0; col < 5; col++) {
		for (let row = 0; row < 4; row++) {
			// Check 2-letter words
			const twoLetter = board.tiles[row][col].letter + board.tiles[row + 1][col].letter;
			if (simpleWordPatterns.includes(twoLetter)) {
				words.push(new Word(twoLetter, [[row, col], [row + 1, col]]));
			}
			
			// Check 3-letter words
			if (row < 3) {
				const threeLetter = board.tiles[row][col].letter + board.tiles[row + 1][col].letter + board.tiles[row + 2][col].letter;
				if (simpleWordPatterns.includes(threeLetter)) {
					words.push(new Word(threeLetter, [[row, col], [row + 1, col], [row + 2, col]]));
				}
			}
		}
	}
	
	return words;
}

async function testWordPlayingProcess(page: Page, word: Word): Promise<void> {
	try {
		console.log(`   🎯 Playing word: ${word.letters}`);
		console.log(`   📍 Positions: ${word.positions.map(p => `[${p[0]},${p[1]}]`).join(' -> ')}`);
		
		// Use the playWord function from gameInteraction
		await playWord(page, word.positions);
		
		console.log('   ✅ Word playing completed successfully!');
		
	} catch (error) {
		console.error('   ❌ Word playing failed:', error);
		
		// Try manual tile clicking as fallback
		console.log('   🔄 Trying manual tile clicking...');
		await testManualTileClicking(page, word);
	}
}

async function testManualTileClicking(page: Page, word: Word): Promise<void> {
	try {
		// Clear any existing selection
		const clearButton = await page.$('button:has-text("Clear")');
		if (clearButton) {
			await clearButton.click();
			await page.waitForTimeout(500);
		}
		
		// Click each tile manually
		for (const [row, col] of word.positions) {
			console.log(`   🖱️  Clicking tile at [${row}, ${col}]`);
			
			// Try multiple selector strategies for clicking tiles
			const tileSelectors = [
				`.grid.grid-cols-5 > div:nth-child(${row * 5 + col + 1})`,
				`.grid > div:nth-child(${row * 5 + col + 1})`,
			];
			
			let clicked = false;
			for (const selector of tileSelectors) {
				try {
					const tile = await page.$(selector);
					if (tile) {
						await tile.click();
						await page.waitForTimeout(300);
						clicked = true;
						console.log(`      ✅ Clicked using selector: ${selector}`);
						break;
					}
				} catch (error) {
					console.log(`      ⚠️  Failed with selector: ${selector}`);
				}
			}
			
			if (!clicked) {
				throw new Error(`Could not click tile at [${row}, ${col}]`);
			}
		}
		
		console.log('   ✅ Manual tile clicking completed!');
		
	} catch (error) {
		console.error('   ❌ Manual tile clicking failed:', error);
		throw error;
	}
}

// Run the test
console.log('🚀 Starting word playing test...');
main().catch((error) => {
	console.error('💥 Script failed with error:', error);
	process.exit(1);
});
