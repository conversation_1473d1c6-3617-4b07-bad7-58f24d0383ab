// See https://svelte.dev/docs/kit/types#app.d.ts
// for information about these interfaces
declare global {
	namespace App {
		// interface Error {}
		// interface Locals {}
		// interface PageData {}
		// interface PageState {}
		interface Platform {
			env: {
				DB: D1Database;
				BROWSER: Fetcher;
				NODE_ENV?: string;
			};
			context: {
				waitUntil(promise: Promise<any>): void;
			};
			caches: CacheStorage & { default: Cache };
		}
	}

	/**
	 * Cloudflare D1 Database types
	 */
	interface D1Database {
		prepare(query: string): D1PreparedStatement;
		dump(): Promise<ArrayBuffer>;
		batch(statements: D1PreparedStatement[]): Promise<D1Result[]>;
		exec(query: string): Promise<D1ExecResult>;
	}

	interface D1PreparedStatement {
		bind(...values: any[]): D1PreparedStatement;
		first<T = any>(colName?: string): Promise<T | null>;
		run(): Promise<D1Result>;
		all<T = any>(): Promise<D1Result<T>>;
	}

	interface D1Result<T = any> {
		results?: T[];
		success: boolean;
		error?: string;
		meta: {
			duration: number;
			size_after: number;
			rows_read: number;
			rows_written: number;
		};
	}

	interface D1ExecResult {
		count: number;
		duration: number;
	}

	/**
	 * Cloudflare Browser Rendering types
	 */
	interface Fetcher {
		fetch(input: RequestInfo, init?: RequestInit): Promise<Response>;
	}
}

export {};
