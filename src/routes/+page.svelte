<script lang="ts">
	import Card from '$lib/components/ui/Card.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import LoadingSpinner from '$lib/components/ui/LoadingSpinner.svelte';
	import ErrorMessage from '$lib/components/ui/ErrorMessage.svelte';
	import BoardResultCard from '$lib/components/BoardResultCard.svelte';
	import type { BestLineResult } from '$lib/types';

	// State for recent results
	let recentResults = $state<Array<{ date: string; result: BestLineResult }>>([]);
	let loading = $state(false);
	let error = $state<string | null>(null);

	// Get today's date for quick navigation
	function getTodayDate(): string {
		return new Date().toISOString().split('T')[0];
	}

	// Load recent results (this would typically be done in a load function)
	async function loadRecentResults() {
		loading = true;
		error = null;

		try {
			// In a real implementation, this would fetch from an API endpoint
			// For now, we'll show a placeholder
			await new Promise((resolve) => setTimeout(resolve, 1000)); // Simulate loading

			// Mock data for demonstration
			recentResults = [
				{
					date: '2025-06-16',
					result: {
						total: 142,
						words: ['QUEST', 'BRAVE', 'LIGHT', 'STORM', 'PEACE'],
						perRound: [
							{ word: 'QUEST', score: 28 },
							{ word: 'BRAVE', score: 25 },
							{ word: 'LIGHT', score: 22 },
							{ word: 'STORM', score: 24 },
							{ word: 'PEACE', score: 26 }
						]
					}
				},
				{
					date: '2025-06-15',
					result: {
						total: 138,
						words: ['MAGIC', 'WORLD', 'DREAM', 'POWER', 'SHINE'],
						perRound: [
							{ word: 'MAGIC', score: 26 },
							{ word: 'WORLD', score: 24 },
							{ word: 'DREAM', score: 23 },
							{ word: 'POWER', score: 27 },
							{ word: 'SHINE', score: 25 }
						]
					}
				}
			];
		} catch (err) {
			error = 'Failed to load recent results';
			console.error('Error loading recent results:', err);
		} finally {
			loading = false;
		}
	}

	// Load results on component mount
	$effect(() => {
		loadRecentResults();
	});
</script>

<svelte:head>
	<title>LettersBot - AI-Powered Letters Game Solver</title>
	<meta
		name="description"
		content="LettersBot uses advanced AI to find optimal solutions for the daily Letters game puzzle. View results, compare strategies, and track performance."
	/>
</svelte:head>

<div class="min-h-screen bg-gray-50">
	<!-- Hero Section -->
	<div class="bg-gradient-to-br from-blue-600 to-blue-800 text-white">
		<div class="mx-auto max-w-7xl px-4 py-16 sm:px-6 lg:px-8">
			<div class="text-center">
				<h1 class="mb-6 text-4xl font-bold md:text-6xl">🤖 LettersBot</h1>
				<p class="mb-8 text-xl text-blue-100 md:text-2xl">AI-Powered Letters Game Solver</p>
				<p class="mx-auto mb-8 max-w-2xl text-lg text-blue-200">
					Advanced beam search algorithm finds optimal word sequences for the daily Letters game
					puzzle. Compare greedy vs. optimal strategies and track performance over time.
				</p>
				<div class="flex flex-col justify-center gap-4 sm:flex-row">
					<Button variant="secondary" size="lg" href="/board/{getTodayDate()}">
						{#snippet children()}
							View Today's Results
						{/snippet}
					</Button>
					<Button
						variant="outline"
						size="lg"
						class="border-white text-white hover:bg-white hover:text-blue-600"
						href="#recent-results"
					>
						{#snippet children()}
							Browse Recent Results
						{/snippet}
					</Button>
				</div>
			</div>
		</div>
	</div>

	<!-- Features Section -->
	<div class="mx-auto max-w-7xl px-4 py-16 sm:px-6 lg:px-8">
		<div class="mb-12 text-center">
			<h2 class="mb-4 text-3xl font-bold text-gray-900">How LettersBot Works</h2>
			<p class="mx-auto max-w-2xl text-lg text-gray-600">
				Our advanced solver uses beam search with branch-and-bound optimization to find the best
				possible word sequences.
			</p>
		</div>

		<div class="grid grid-cols-1 gap-8 md:grid-cols-3">
			<Card>
				{#snippet children()}
					<div class="text-center">
						<div
							class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100"
						>
							<svg
								class="h-6 w-6 text-blue-600"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
								/>
							</svg>
						</div>
						<h3 class="mb-2 text-lg font-semibold text-gray-900">Smart Algorithm</h3>
						<p class="text-gray-600">
							Uses beam search with Hungarian algorithm optimization to explore the most promising
							word sequences efficiently.
						</p>
					</div>
				{/snippet}
			</Card>

			<Card>
				{#snippet children()}
					<div class="text-center">
						<div
							class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-green-100"
						>
							<svg
								class="h-6 w-6 text-green-600"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
								/>
							</svg>
						</div>
						<h3 class="mb-2 text-lg font-semibold text-gray-900">Daily Analysis</h3>
						<p class="text-gray-600">
							Automatically solves each day's puzzle and compares optimal solutions against greedy
							strategies.
						</p>
					</div>
				{/snippet}
			</Card>

			<Card>
				{#snippet children()}
					<div class="text-center">
						<div
							class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-purple-100"
						>
							<svg
								class="h-6 w-6 text-purple-600"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
								/>
							</svg>
						</div>
						<h3 class="mb-2 text-lg font-semibold text-gray-900">Performance Tracking</h3>
						<p class="text-gray-600">
							Track improvements over time and see detailed breakdowns of optimal vs. greedy
							performance.
						</p>
					</div>
				{/snippet}
			</Card>
		</div>
	</div>

	<!-- Recent Results Section -->
	<div id="recent-results" class="bg-white py-16">
		<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
			<div class="mb-12 text-center">
				<h2 class="mb-4 text-3xl font-bold text-gray-900">Recent Results</h2>
				<p class="text-lg text-gray-600">
					Latest solver results from the daily Letters game puzzles
				</p>
			</div>

			{#if loading}
				<div class="py-12 text-center">
					<LoadingSpinner size="lg" />
					<p class="mt-4 text-gray-600">Loading recent results...</p>
				</div>
			{:else if error}
				<div class="mx-auto max-w-md">
					<ErrorMessage title="Failed to Load Results" message={error} type="error" />
				</div>
			{:else if recentResults.length > 0}
				<div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
					{#each recentResults as { date, result }}
						<BoardResultCard {date} {result} />
					{/each}
				</div>
				<div class="mt-8 text-center">
					<Button variant="outline" size="lg">
						{#snippet children()}
							View All Results
						{/snippet}
					</Button>
				</div>
			{:else}
				<div class="py-12 text-center">
					<p class="text-gray-600">No recent results available</p>
				</div>
			{/if}
		</div>
	</div>

	<!-- Footer -->
	<footer class="bg-gray-900 py-8 text-white">
		<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
			<div class="text-center">
				<p class="text-gray-400">
					LettersBot - Powered by advanced AI algorithms for optimal Letters game solutions
				</p>
				<p class="mt-2 text-sm text-gray-500">
					Built with SvelteKit, Cloudflare Workers, and lots of ☕
				</p>
			</div>
		</div>
	</footer>
</div>
