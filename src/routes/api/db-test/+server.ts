import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { initializeDatabase, verifyDatabaseSchema, getDatabaseStats } from '$lib/db';

export const GET: RequestHandler = async ({ platform }) => {
	try {
		// Check if we have access to the D1 database
		if (!platform?.env?.DB) {
			return new Response(
				JSON.stringify({
					success: false,
					error: 'D1 database not available',
					message: 'Make sure you are running with Wrangler and D1 is configured'
				}),
				{
					status: 500,
					headers: { 'Content-Type': 'application/json' }
				}
			);
		}

		// Test basic database connectivity
		const db = platform.env.DB;

		// Simple query to test connection
		const connectionTest = await db.prepare('SELECT 1 as test').first();

		// Test if our best_lines table exists
		const tablesQuery = await db
			.prepare(
				`
			SELECT name FROM sqlite_master
			WHERE type='table' AND name = 'best_lines'
		`
			)
			.all();

		const tableExists = (tablesQuery.results?.length || 0) > 0;

		// If table doesn't exist, try to initialize the database
		if (!tableExists) {
			console.log('best_lines table not found, initializing database...');
			try {
				await initializeDatabase(db);
				console.log('Database initialized successfully');
			} catch (initError) {
				console.error('Failed to initialize database:', initError);
				return new Response(
					JSON.stringify({
						success: false,
						error: 'Database initialization failed',
						message: initError instanceof Error ? initError.message : 'Unknown error',
						timestamp: new Date().toISOString()
					}),
					{
						status: 500,
						headers: { 'Content-Type': 'application/json' }
					}
				);
			}
		}

		// Verify schema
		const schemaValid = await verifyDatabaseSchema(db);

		// Get database statistics (skip for now due to PRAGMA issues in D1)
		let stats = null;
		try {
			stats = await getDatabaseStats(db);
		} catch (statsError) {
			console.warn('Failed to get database stats (this is okay):', statsError);
			stats = { error: 'Stats unavailable in D1 environment' };
		}

		return new Response(
			JSON.stringify({
				success: true,
				message: 'D1 database connection and schema verification successful',
				data: {
					connectionTest,
					tableExists,
					schemaValid,
					stats,
					timestamp: new Date().toISOString()
				}
			}),
			{
				status: 200,
				headers: { 'Content-Type': 'application/json' }
			}
		);
	} catch (error) {
		console.error('Database test error:', error);

		return new Response(
			JSON.stringify({
				success: false,
				error: 'Database test failed',
				message: error instanceof Error ? error.message : 'Unknown error',
				timestamp: new Date().toISOString()
			}),
			{
				status: 500,
				headers: { 'Content-Type': 'application/json' }
			}
		);
	}
};
