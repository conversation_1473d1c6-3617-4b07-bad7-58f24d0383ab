import { expect, test, type Page } from '@playwright/test';

test.describe('LettersBot Application', () => {
	test('home page loads correctly', async ({ page }) => {
		await page.goto('/');

		// Check that the main heading is visible
		await expect(page.locator('h1')).toBeVisible();
		await expect(page.locator('h1')).toContainText('LettersBot');

		// Check that key sections are present
		await expect(page.locator('text=AI-Powered Letters Game Solver')).toBeVisible();
		await expect(page.locator('text=How LettersBot Works')).toBeVisible();
		await expect(page.locator('text=Recent Results')).toBeVisible();
	});

	test('navigation to board results works', async ({ page }) => {
		await page.goto('/');

		// Look for a "View Today's Results" button or similar
		const todayButton = page.locator("text=View Today's Results").first();
		if (await todayButton.isVisible()) {
			await todayButton.click();
			// Should navigate to a board results page
			await expect(page.url()).toMatch(/\/board\/\d{4}-\d{2}-\d{2}/);
		}
	});

	test('board results page handles missing data gracefully', async ({ page }) => {
		// Test with a future date that shouldn't have results
		const futureDate = new Date();
		futureDate.setDate(futureDate.getDate() + 10);
		const futureDateStr = futureDate.toISOString().split('T')[0];

		await page.goto(`/board/${futureDateStr}`);

		// Should show an error message for future dates
		await expect(page.locator('text=No Results Available')).toBeVisible();
		await expect(page.locator('text=Cannot retrieve results for future dates')).toBeVisible();
	});

	test('board results page displays data correctly', async ({ page }) => {
		// Test with a date that might have mock data
		await page.goto('/board/2025-06-16');

		// Wait for the page to load
		await page.waitForLoadState('networkidle');

		// Check if we have results or an appropriate error message
		const hasResults = await page.locator('text=Optimal Total').isVisible();
		const hasError = await page.locator('text=No Results Available').isVisible();

		expect(hasResults || hasError).toBe(true);

		if (hasResults) {
			// If we have results, check the structure
			await expect(page.locator('text=Optimal Total')).toBeVisible();
			await expect(page.locator('text=Words Played')).toBeVisible();
			await expect(page.locator('text=vs. Greedy')).toBeVisible();
		}
	});
});
