import { expect, test } from '@playwright/test';

/**
 * End-to-end tests for the complete LettersBot solver workflow
 * 
 * These tests verify the integration between all components:
 * - Board scraping and parsing
 * - Word finding algorithms
 * - Game interaction
 * - Result storage and retrieval
 */

test.describe('LettersBot Solver Workflow', () => {
	test.beforeEach(async ({ page }) => {
		// Set longer timeout for solver operations
		page.setDefaultTimeout(60000);
	});

	test('solver can process a test board', async ({ page }) => {
		// Navigate to the application
		await page.goto('/');
		
		// Check if we can access the solver functionality
		// This would typically involve testing against a mock or test board
		
		// For now, we'll test the UI components that would display solver results
		await expect(page.locator('h1')).toContainText('LettersBot');
		
		// Test that the application can handle board result display
		const recentResultsSection = page.locator('text=Recent Results');
		await expect(recentResultsSection).toBeVisible();
		
		// Check if loading states work
		const loadingSpinner = page.locator('.animate-spin');
		if (await loadingSpinner.isVisible()) {
			// Wait for loading to complete
			await expect(loadingSpinner).not.toBeVisible({ timeout: 30000 });
		}
	});

	test('board result card displays correctly', async ({ page }) => {
		await page.goto('/');
		
		// Wait for the page to load completely
		await page.waitForLoadState('networkidle');
		
		// Look for board result cards (if any are displayed)
		const resultCards = page.locator('[class*="bg-white"][class*="shadow"]');
		const cardCount = await resultCards.count();
		
		if (cardCount > 0) {
			// Test the first card
			const firstCard = resultCards.first();
			await expect(firstCard).toBeVisible();
			
			// Check for expected elements in the card
			const hasScore = await firstCard.locator('text=/\\d+/').first().isVisible();
			expect(hasScore).toBe(true);
			
			// Look for action buttons
			const detailsButton = firstCard.locator('button:has-text("Details")');
			const viewButton = firstCard.locator('a:has-text("View")');
			
			if (await detailsButton.isVisible()) {
				await detailsButton.click();
				// Should expand to show more details
				await expect(firstCard.locator('text=Greedy vs. Optimal')).toBeVisible();
			}
			
			if (await viewButton.isVisible()) {
				await viewButton.click();
				// Should navigate to detailed results page
				await expect(page.url()).toMatch(/\/board\/\d{4}-\d{2}-\d{2}/);
			}
		}
	});

	test('error handling works correctly', async ({ page }) => {
		// Test error handling by navigating to an invalid board date
		await page.goto('/board/invalid-date');
		
		// Should show error message
		await expect(page.locator('text=No Results Available')).toBeVisible();
		await expect(page.locator('text=Invalid date format')).toBeVisible();
		
		// Should have a back button
		const backButton = page.locator('text=Back to Home');
		await expect(backButton).toBeVisible();
		
		// Test navigation back to home
		await backButton.click();
		await expect(page.url()).toBe(new URL('/', page.url()).href);
	});

	test('responsive design works on mobile', async ({ page }) => {
		// Set mobile viewport
		await page.setViewportSize({ width: 375, height: 667 });
		
		await page.goto('/');
		
		// Check that the layout adapts to mobile
		await expect(page.locator('h1')).toBeVisible();
		
		// Navigation should work on mobile
		const todayButton = page.locator("text=View Today's Results").first();
		if (await todayButton.isVisible()) {
			await todayButton.click();
			await expect(page.url()).toMatch(/\/board\/\d{4}-\d{2}-\d{2}/);
		}
		
		// Go to a board results page and check mobile layout
		await page.goto('/board/2025-06-16');
		await page.waitForLoadState('networkidle');
		
		// Check that content is readable on mobile
		const hasResults = await page.locator('text=Optimal Total').isVisible();
		const hasError = await page.locator('text=No Results Available').isVisible();
		
		expect(hasResults || hasError).toBe(true);
	});

	test('performance is acceptable', async ({ page }) => {
		// Test page load performance
		const startTime = Date.now();
		
		await page.goto('/');
		await page.waitForLoadState('networkidle');
		
		const loadTime = Date.now() - startTime;
		
		// Page should load within 5 seconds
		expect(loadTime).toBeLessThan(5000);
		
		// Test navigation performance
		const navStartTime = Date.now();
		
		const todayButton = page.locator("text=View Today's Results").first();
		if (await todayButton.isVisible()) {
			await todayButton.click();
			await page.waitForLoadState('networkidle');
			
			const navTime = Date.now() - navStartTime;
			
			// Navigation should be fast
			expect(navTime).toBeLessThan(3000);
		}
	});

	test('accessibility features work', async ({ page }) => {
		await page.goto('/');
		
		// Check for proper heading structure
		const h1 = page.locator('h1');
		await expect(h1).toBeVisible();
		
		// Check that buttons are keyboard accessible
		const buttons = page.locator('button, a[role="button"]');
		const buttonCount = await buttons.count();
		
		if (buttonCount > 0) {
			const firstButton = buttons.first();
			await firstButton.focus();
			
			// Should be focusable
			await expect(firstButton).toBeFocused();
		}
		
		// Check for alt text on images (if any)
		const images = page.locator('img');
		const imageCount = await images.count();
		
		for (let i = 0; i < imageCount; i++) {
			const img = images.nth(i);
			const alt = await img.getAttribute('alt');
			expect(alt).toBeTruthy();
		}
	});

	test('share functionality works', async ({ page }) => {
		// Navigate to a board results page
		await page.goto('/board/2025-06-16');
		await page.waitForLoadState('networkidle');
		
		// Look for share button
		const shareButton = page.locator('button:has-text("Share")');
		
		if (await shareButton.isVisible()) {
			// Mock the navigator.share API if it exists
			await page.addInitScript(() => {
				if ('navigator' in window) {
					(window.navigator as any).share = async (data: any) => {
						console.log('Share called with:', data);
						return Promise.resolve();
					};
				}
			});
			
			await shareButton.click();
			
			// Should not throw an error
			// In a real test, we might check that the share data is correct
		}
	});
});
