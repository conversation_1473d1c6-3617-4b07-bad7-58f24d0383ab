import { expect, test } from '@playwright/test';

/**
 * Integration tests for the LettersBot solver with mock data
 * 
 * These tests verify that the solver algorithms work correctly
 * with known test cases and produce expected results.
 */

test.describe('LettersBot Solver Integration', () => {
	test.beforeEach(async ({ page }) => {
		// Set longer timeout for solver operations
		page.setDefaultTimeout(30000);
	});

	test('solver produces consistent results', async ({ page }) => {
		// Navigate to the application
		await page.goto('/');
		
		// Inject test data and solver functions for testing
		await page.addInitScript(() => {
			// Mock the bestWordsFast function for testing
			(window as any).testSolver = {
				// Test board configuration
				testBoard: [
					['H', 'E', 'L', 'L', 'O'],
					['W', 'O', 'R', 'L', 'D'],
					['T', 'E', 'S', 'T', 'S'],
					['G', 'A', 'M', 'E', 'S'],
					['S', 'O', 'L', 'V', 'E']
				],
				
				// Expected words for this board
				expectedWords: ['HELLO', 'WORLD', 'TEST', 'GAME', 'SOLVE'],
				
				// Mock solver function
				solve: function(board: string[][]) {
					// Simple mock that returns expected results
					return {
						total: 150,
						words: this.expectedWords,
						perRound: [
							{ word: 'HELLO', score: 30 },
							{ word: 'WORLD', score: 30 },
							{ word: 'TEST', score: 30 },
							{ word: 'GAME', score: 30 },
							{ word: 'SOLVE', score: 30 }
						]
					};
				}
			};
		});
		
		// Test the solver with the mock data
		const result = await page.evaluate(() => {
			const solver = (window as any).testSolver;
			return solver.solve(solver.testBoard);
		});
		
		// Verify the results
		expect(result.total).toBe(150);
		expect(result.words).toHaveLength(5);
		expect(result.words).toContain('HELLO');
		expect(result.words).toContain('WORLD');
		expect(result.perRound).toHaveLength(5);
	});

	test('board parsing works correctly', async ({ page }) => {
		await page.goto('/');
		
		// Inject board parsing test
		await page.addInitScript(() => {
			(window as any).testBoardParsing = {
				// Mock HTML structure similar to the Letters game
				createMockBoard: function() {
					const board = document.createElement('div');
					board.className = 'game-board';
					
					const letters = ['H', 'E', 'L', 'L', 'O', 'W', 'O', 'R', 'L', 'D', 'T', 'E', 'S', 'T', 'S', 'G', 'A', 'M', 'E', 'S', 'S', 'O', 'L', 'V', 'E'];
					
					letters.forEach((letter, index) => {
						const tile = document.createElement('div');
						tile.className = 'tile';
						tile.textContent = letter;
						tile.dataset.row = Math.floor(index / 5).toString();
						tile.dataset.col = (index % 5).toString();
						board.appendChild(tile);
					});
					
					return board;
				},
				
				// Mock board parsing function
				parseBoard: function(boardElement: HTMLElement) {
					const tiles = boardElement.querySelectorAll('.tile');
					const board: string[][] = Array(5).fill(null).map(() => Array(5).fill(''));
					
					tiles.forEach((tile) => {
						const row = parseInt(tile.getAttribute('data-row') || '0');
						const col = parseInt(tile.getAttribute('data-col') || '0');
						board[row][col] = tile.textContent || '';
					});
					
					return board;
				}
			};
		});
		
		// Test board parsing
		const parsedBoard = await page.evaluate(() => {
			const parser = (window as any).testBoardParsing;
			const mockBoard = parser.createMockBoard();
			return parser.parseBoard(mockBoard);
		});
		
		// Verify the parsed board
		expect(parsedBoard).toHaveLength(5);
		expect(parsedBoard[0]).toEqual(['H', 'E', 'L', 'L', 'O']);
		expect(parsedBoard[1]).toEqual(['W', 'O', 'R', 'L', 'D']);
		expect(parsedBoard[4]).toEqual(['S', 'O', 'L', 'V', 'E']);
	});

	test('word validation works correctly', async ({ page }) => {
		await page.goto('/');
		
		// Inject word validation test
		await page.addInitScript(() => {
			(window as any).testWordValidation = {
				// Mock word validation function
				isValidWord: function(word: string, board: string[][]) {
					// Simple validation - check if all letters exist on board
					const boardLetters = board.flat().join('');
					return word.split('').every(letter => boardLetters.includes(letter));
				},
				
				// Test cases
				testCases: [
					{ word: 'HELLO', board: [['H', 'E', 'L', 'L', 'O']], expected: true },
					{ word: 'WORLD', board: [['W', 'O', 'R', 'L', 'D']], expected: true },
					{ word: 'INVALID', board: [['H', 'E', 'L', 'L', 'O']], expected: false }
				]
			};
		});
		
		// Test word validation
		const validationResults = await page.evaluate(() => {
			const validator = (window as any).testWordValidation;
			return validator.testCases.map((testCase: any) => ({
				word: testCase.word,
				expected: testCase.expected,
				actual: validator.isValidWord(testCase.word, testCase.board)
			}));
		});
		
		// Verify validation results
		validationResults.forEach(result => {
			expect(result.actual).toBe(result.expected);
		});
	});

	test('scoring calculation is accurate', async ({ page }) => {
		await page.goto('/');
		
		// Inject scoring test
		await page.addInitScript(() => {
			(window as any).testScoring = {
				// Mock scoring function
				calculateScore: function(word: string, positions: number[][]) {
					// Simple scoring - base score plus position multipliers
					let score = word.length * 5; // Base score
					
					// Add position-based multipliers
					positions.forEach(([row, col]) => {
						if (row === 2 && col === 2) score *= 2; // Center tile double
						if ((row + col) % 2 === 0) score += 2; // Even position bonus
					});
					
					return score;
				},
				
				// Test cases
				testWords: [
					{ word: 'HELLO', positions: [[0,0], [0,1], [0,2], [0,3], [0,4]] },
					{ word: 'TEST', positions: [[2,0], [2,1], [2,2], [2,3]] }
				]
			};
		});
		
		// Test scoring calculation
		const scoringResults = await page.evaluate(() => {
			const scorer = (window as any).testScoring;
			return scorer.testWords.map((testWord: any) => ({
				word: testWord.word,
				score: scorer.calculateScore(testWord.word, testWord.positions)
			}));
		});
		
		// Verify scoring results
		expect(scoringResults).toHaveLength(2);
		scoringResults.forEach(result => {
			expect(result.score).toBeGreaterThan(0);
			expect(typeof result.score).toBe('number');
		});
	});

	test('performance meets requirements', async ({ page }) => {
		await page.goto('/');
		
		// Test solver performance with a larger mock board
		await page.addInitScript(() => {
			(window as any).testPerformance = {
				// Create a larger test board
				createLargeBoard: function() {
					return Array(5).fill(null).map(() => 
						Array(5).fill(null).map(() => 
							String.fromCharCode(65 + Math.floor(Math.random() * 26))
						)
					);
				},
				
				// Mock solver with performance tracking
				solveWithTiming: function(board: string[][]) {
					const startTime = performance.now();
					
					// Simulate solver work
					const words = ['WORD1', 'WORD2', 'WORD3', 'WORD4', 'WORD5'];
					const result = {
						total: 100,
						words: words,
						perRound: words.map(word => ({ word, score: 20 }))
					};
					
					const endTime = performance.now();
					return {
						result,
						timeMs: endTime - startTime
					};
				}
			};
		});
		
		// Test performance
		const performanceResult = await page.evaluate(() => {
			const perf = (window as any).testPerformance;
			const board = perf.createLargeBoard();
			return perf.solveWithTiming(board);
		});
		
		// Verify performance requirements
		expect(performanceResult.timeMs).toBeLessThan(1000); // Should solve in under 1 second
		expect(performanceResult.result.words).toHaveLength(5);
		expect(performanceResult.result.total).toBeGreaterThan(0);
	});
});
