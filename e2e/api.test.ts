import { expect, test } from '@playwright/test';

test.describe('LettersBot API Endpoints', () => {
	test('webhook endpoint requires authentication', async ({ request }) => {
		// Test without authentication
		const response = await request.post('/api/run', {
			data: { date: '2025-06-17' }
		});
		
		expect(response.status()).toBe(401);
		const data = await response.json();
		expect(data.success).toBe(false);
		expect(data.error).toBe('Unauthorized');
	});

	test('webhook endpoint validates request body', async ({ request }) => {
		// Test with invalid date format
		const response = await request.post('/api/run', {
			headers: {
				'Authorization': 'Bearer test-secret-key'
			},
			data: { date: 'invalid-date' }
		});
		
		expect(response.status()).toBe(400);
		const data = await response.json();
		expect(data.success).toBe(false);
		expect(data.message).toContain('Invalid date format');
	});

	test('webhook endpoint accepts valid requests', async ({ request }) => {
		// Test with valid authentication and data
		const response = await request.post('/api/run', {
			headers: {
				'Authorization': 'Bearer test-secret-key'
			},
			data: { 
				date: '2025-06-17',
				force: true 
			}
		});
		
		// Should either start a job (202) or return existing result (200)
		expect([200, 202, 503]).toContain(response.status());
		
		const data = await response.json();
		if (response.status() === 503) {
			// Service unavailable (expected in test environment without D1/Browser)
			expect(data.success).toBe(false);
			expect(data.message).toContain('not available');
		} else {
			expect(data.success).toBe(true);
		}
	});

	test('board endpoint validates date format', async ({ request }) => {
		const response = await request.get('/board/invalid-date');
		
		expect(response.status()).toBe(400);
		const data = await response.json();
		expect(data.success).toBe(false);
		expect(data.message).toContain('Invalid date format');
	});

	test('board endpoint rejects future dates', async ({ request }) => {
		const futureDate = new Date();
		futureDate.setDate(futureDate.getDate() + 1);
		const futureDateStr = futureDate.toISOString().split('T')[0];
		
		const response = await request.get(`/board/${futureDateStr}`);
		
		expect(response.status()).toBe(400);
		const data = await response.json();
		expect(data.success).toBe(false);
		expect(data.message).toContain('Cannot retrieve results for future dates');
	});

	test('board endpoint handles missing results', async ({ request }) => {
		// Test with a past date that likely doesn't have results
		const response = await request.get('/board/2020-01-01');
		
		// Should either return 404 (no results) or 503 (service unavailable)
		expect([404, 503]).toContain(response.status());
		
		const data = await response.json();
		expect(data.success).toBe(false);
		
		if (response.status() === 404) {
			expect(data.error).toBe('Not Found');
		} else {
			expect(data.message).toContain('not available');
		}
	});

	test('board endpoint supports format parameter', async ({ request }) => {
		const response = await request.get('/board/2025-06-16?format=summary');
		
		// Should either return data or service unavailable
		if (response.status() === 200) {
			const data = await response.json();
			expect(data.success).toBe(true);
			expect(data.data).toHaveProperty('total');
			expect(data.data).toHaveProperty('words');
			expect(data.data).toHaveProperty('wordCount');
			// Summary format should not include perRound details
			expect(data.data).not.toHaveProperty('perRound');
		} else {
			// Service unavailable is acceptable in test environment
			expect([404, 503]).toContain(response.status());
		}
	});

	test('board endpoint supports CORS', async ({ request }) => {
		const response = await request.fetch('/board/2025-06-16', {
			method: 'OPTIONS'
		});
		
		expect(response.status()).toBe(200);
		expect(response.headers()['access-control-allow-origin']).toBe('*');
		expect(response.headers()['access-control-allow-methods']).toContain('GET');
	});

	test('webhook GET endpoint returns service status', async ({ request }) => {
		const response = await request.get('/api/run');
		
		expect(response.status()).toBe(200);
		const data = await response.json();
		expect(data.success).toBe(true);
		expect(data.data).toHaveProperty('service');
		expect(data.data).toHaveProperty('version');
		expect(data.data).toHaveProperty('timestamp');
		expect(data.data).toHaveProperty('services');
	});
});
