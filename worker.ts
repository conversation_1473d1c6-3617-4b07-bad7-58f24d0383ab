/**
 * Cloudflare Worker for Scheduled Daily Letters Bot Runs
 * 
 * This worker handles the cron trigger and calls the main application's
 * webhook endpoint to initiate daily solver runs.
 */

interface Env {
	WEBHOOK_SECRET: string;
	LETTERS_BOT_DOMAIN: string;
	NODE_ENV?: string;
}

interface ScheduledEvent {
	type: 'scheduled';
	scheduledTime: number;
	cron: string;
}

interface ExecutionContext {
	waitUntil(promise: Promise<any>): void;
	passThroughOnException(): void;
}

/**
 * Main worker export with scheduled handler
 */
export default {
	/**
	 * Scheduled handler for daily solver runs
	 * Triggered by cron: "0 6 * * *" (daily at 6 AM UTC)
	 */
	async scheduled(
		event: ScheduledEvent,
		env: Env,
		ctx: ExecutionContext
	): Promise<void> {
		const startTime = Date.now();
		const scheduledTime = new Date(event.scheduledTime);
		
		console.log(`[LettersBot-Scheduler] Daily solver triggered at ${scheduledTime.toISOString()}`);
		console.log(`[LettersBot-Scheduler] Cron pattern: ${event.cron}`);

		try {
			// Validate required environment variables
			if (!env.WEBHOOK_SECRET) {
				throw new Error('WEBHOOK_SECRET environment variable is required');
			}

			if (!env.LETTERS_BOT_DOMAIN) {
				throw new Error('LETTERS_BOT_DOMAIN environment variable is required');
			}

			// Prepare the webhook request
			const webhookUrl = `${env.LETTERS_BOT_DOMAIN}/api/run`;
			const currentDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

			console.log(`[LettersBot-Scheduler] Calling webhook: ${webhookUrl}`);
			console.log(`[LettersBot-Scheduler] Target date: ${currentDate}`);

			// Call the main application's webhook endpoint
			const response = await fetch(webhookUrl, {
				method: 'POST',
				headers: {
					'Authorization': `Bearer ${env.WEBHOOK_SECRET}`,
					'Content-Type': 'application/json',
					'User-Agent': 'LettersBot-Scheduler/1.0',
					'X-Scheduled-Time': scheduledTime.toISOString(),
					'X-Cron-Pattern': event.cron
				},
				body: JSON.stringify({
					date: currentDate,
					force: false, // Don't force re-solve if already exists
					source: 'scheduled', // Indicate this came from the scheduler
					scheduledTime: event.scheduledTime,
					cronPattern: event.cron
				})
			});

			// Handle the response
			if (!response.ok) {
				const errorText = await response.text();
				console.error(`[LettersBot-Scheduler] Webhook call failed: ${response.status} ${response.statusText}`);
				console.error(`[LettersBot-Scheduler] Error response: ${errorText}`);
				throw new Error(`Webhook call failed: ${response.status} ${response.statusText}`);
			}

			const result = await response.json();
			const duration = Date.now() - startTime;

			console.log(`[LettersBot-Scheduler] Webhook call successful (${duration}ms):`, {
				success: result.success,
				message: result.message,
				date: result.data?.date,
				jobStarted: result.data?.jobStarted
			});

			// Use waitUntil to ensure logging completes
			ctx.waitUntil(
				Promise.resolve().then(() => {
					console.log(`[LettersBot-Scheduler] Daily solver job initiated successfully for ${currentDate}`);
				})
			);

		} catch (error) {
			const duration = Date.now() - startTime;
			console.error(`[LettersBot-Scheduler] Error in scheduled handler (${duration}ms):`, {
				error: error instanceof Error ? error.message : String(error),
				scheduledTime: scheduledTime.toISOString(),
				cron: event.cron
			});
			
			// In a production system, you might want to:
			// 1. Send alerts to monitoring systems (e.g., Sentry, PagerDuty)
			// 2. Store error information in a database for debugging
			// 3. Implement retry logic with exponential backoff
			// 4. Send notifications to administrators
			
			// For now, we'll just re-throw to mark the scheduled event as failed
			throw error;
		}
	},

	/**
	 * HTTP handler for manual testing and health checks
	 */
	async fetch(
		request: Request,
		env: Env,
		ctx: ExecutionContext
	): Promise<Response> {
		const url = new URL(request.url);

		// Health check endpoint
		if (url.pathname === '/health') {
			return new Response(JSON.stringify({
				status: 'healthy',
				service: 'LettersBot-Scheduler',
				timestamp: new Date().toISOString(),
				environment: env.NODE_ENV || 'unknown'
			}), {
				headers: { 'Content-Type': 'application/json' }
			});
		}

		// Manual trigger endpoint (for testing)
		if (url.pathname === '/trigger' && request.method === 'POST') {
			// Simulate a scheduled event
			const mockEvent: ScheduledEvent = {
				type: 'scheduled',
				scheduledTime: Date.now(),
				cron: 'manual-trigger'
			};

			try {
				await this.scheduled(mockEvent, env, ctx);
				return new Response(JSON.stringify({
					success: true,
					message: 'Manual trigger completed successfully',
					timestamp: new Date().toISOString()
				}), {
					headers: { 'Content-Type': 'application/json' }
				});
			} catch (error) {
				return new Response(JSON.stringify({
					success: false,
					error: error instanceof Error ? error.message : String(error),
					timestamp: new Date().toISOString()
				}), {
					status: 500,
					headers: { 'Content-Type': 'application/json' }
				});
			}
		}

		// Default response
		return new Response(JSON.stringify({
			service: 'LettersBot-Scheduler',
			message: 'Cloudflare Worker for scheduled Letters game solving',
			endpoints: {
				health: '/health',
				manualTrigger: 'POST /trigger'
			},
			timestamp: new Date().toISOString()
		}), {
			headers: { 'Content-Type': 'application/json' }
		});
	}
};
