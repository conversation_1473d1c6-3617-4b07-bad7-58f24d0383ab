# LettersBot Scheduler Setup Guide

This guide explains how to set up automated daily runs for the LettersBot using Cloudflare Cron Triggers.

## Architecture Overview

The scheduler consists of two components:

1. **Main Application** (Cloudflare Pages): Handles the web interface and webhook endpoint
2. **Scheduler Worker** (Cloudflare Worker): Handles cron triggers and calls the webhook

```
┌─────────────────┐    cron trigger    ┌──────────────────┐
│ Scheduler Worker│ ──────────────────▶ │ Main Application │
│ (worker.ts)     │    POST /api/run   │ (SvelteKit)      │
└─────────────────┘                    └──────────────────┘
```

## Quick Setup

### 1. Deploy the Main Application First

```bash
# Deploy the main LettersBot application
./scripts/deploy.sh
```

### 2. Deploy the Scheduler Worker

```bash
# Deploy the scheduler worker
./scripts/deploy-scheduler.sh
```

### 3. Configure Secrets

```bash
# Set the webhook secret for the scheduler
wrangler secret put WEBHOOK_SECRET --config wrangler-scheduler.toml --env production

# Enter your webhook secret when prompted (same as used in main app)
```

## Detailed Setup

### 1. Configuration Files

#### Main Application (`wrangler.toml`)
- Handles the SvelteKit application
- Provides the `/api/run` webhook endpoint
- No cron triggers (handled by separate worker)

#### Scheduler Worker (`wrangler-scheduler.toml`)
- Separate Cloudflare Worker
- Contains cron trigger: `"0 6 * * *"` (daily at 6 AM UTC)
- Calls the main application's webhook

### 2. Environment Variables

#### Required for Scheduler Worker:
- `WEBHOOK_SECRET`: Authentication token for webhook calls
- `LETTERS_BOT_DOMAIN`: URL of the main application

#### Update Domain in `wrangler-scheduler.toml`:
```toml
[env.production.vars]
LETTERS_BOT_DOMAIN = "https://your-actual-domain.com"  # Replace with your domain
```

**Note**: Cloudflare Pages only supports "preview" and "production" environments, not "development".

### 3. Deployment Commands

```bash
# Deploy main application
npm run build
wrangler pages deploy build --project-name letters-bot --env production

# Deploy scheduler worker
wrangler deploy --config wrangler-scheduler.toml --env production

# Set secrets
wrangler secret put WEBHOOK_SECRET --config wrangler-scheduler.toml --env production
```

## Testing

### 1. Manual Trigger

Test the scheduler manually:

```bash
# Get your worker subdomain from Cloudflare dashboard
curl -X POST https://letters-bot-scheduler.your-subdomain.workers.dev/trigger
```

### 2. Health Check

```bash
curl https://letters-bot-scheduler.your-subdomain.workers.dev/health
```

### 3. Monitor Logs

```bash
# Watch scheduler logs
wrangler tail --config wrangler-scheduler.toml --env production

# Watch main application logs
wrangler pages deployment tail --project-name letters-bot
```

## Schedule Configuration

### Current Schedule
- **Frequency**: Daily
- **Time**: 6:00 AM UTC
- **Cron Pattern**: `"0 6 * * *"`

### Changing the Schedule

Edit `wrangler-scheduler.toml`:

```toml
[triggers]
crons = ["0 6 * * *"]  # Daily at 6 AM UTC
# crons = ["0 */6 * * *"]  # Every 6 hours
# crons = ["0 12 * * 1"]  # Weekly on Monday at noon
```

Then redeploy:
```bash
wrangler deploy --config wrangler-scheduler.toml --env production
```

## Monitoring and Troubleshooting

### 1. Check Scheduler Status

```bash
# View worker details
wrangler list

# Check cron triggers
wrangler cron trigger list --config wrangler-scheduler.toml
```

### 2. Common Issues

#### Webhook Authentication Fails
- Verify `WEBHOOK_SECRET` is set correctly in both applications
- Check that the secret matches between scheduler and main app

#### Domain Not Reachable
- Update `LETTERS_BOT_DOMAIN` in `wrangler-scheduler.toml`
- Ensure the main application is deployed and accessible

#### Cron Not Triggering
- Check cron syntax in `wrangler-scheduler.toml`
- Verify the worker is deployed with cron triggers enabled
- Monitor logs for any errors

### 3. Debugging

```bash
# View recent executions
wrangler tail --config wrangler-scheduler.toml --env production

# Test webhook endpoint directly
curl -X POST \
  -H "Authorization: Bearer YOUR_WEBHOOK_SECRET" \
  -H "Content-Type: application/json" \
  https://your-domain.com/api/run
```

## Security Considerations

1. **Webhook Secret**: Use a strong, unique secret for webhook authentication
2. **Domain Validation**: Ensure the scheduler only calls your legitimate domain
3. **Error Handling**: Monitor for failed runs and implement alerting
4. **Access Control**: Limit who can modify the scheduler configuration

## Cost Considerations

- **Scheduler Worker**: ~100,000 requests/month on free tier (more than enough for daily runs)
- **Main Application**: Cloudflare Pages free tier supports the application
- **D1 Database**: Free tier includes 5GB storage and 25 million reads/month

## Next Steps

1. Deploy both components using the provided scripts
2. Set up monitoring and alerting for failed runs
3. Consider adding retry logic for failed webhook calls
4. Implement notifications for successful/failed solver runs

## Support

For issues:
1. Check the logs using `wrangler tail`
2. Verify configuration in both `wrangler.toml` files
3. Test components individually (webhook, scheduler)
4. Review Cloudflare Workers documentation for advanced configuration
